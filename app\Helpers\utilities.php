<?php

use App\Classes\SiteConstants;
use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Model\Staff;
use App\Model\v2\CourseType;
use App\Model\v2\Tenant;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

if (! function_exists('authUser')) {
    function authUser()
    {
        // if(auth('api')->user()){}
        if (auth()->user()) {
            return auth()->user();
        } elseif (auth('student')->user()) {
            return auth('student')->user();
        } elseif (auth('agent')->user()) {
            return auth('agent')->user();
        } elseif (auth('agentstaff')->user()) {
            return auth('agentstaff')->user();
        }
    }
}

/* ===== Menu Item Helpers ==== */
if (! function_exists('menuItemsForCurrentUser')) {
    function menuItemsForCurrentUser()
    {
        return \App\Constants\Menu\MenuGenerator::GenerateForUser(authUser());
    }
}

if (! function_exists('menuItemsForPage')) {
    function menuItemsForPage($page)
    {
        return \App\Constants\Menu\MenuGenerator::GenerateForPage($page);
    }
}
/* ===== Menu Item Helpers ==== */

if (! function_exists('encryptIt')) {
    /**
     * @return string
     */
    function encryptIt($string)
    {
        return urlencode(\Illuminate\Support\Facades\Crypt::encrypt($string));
    }
}

if (! function_exists('decryptIt')) {
    /**
     * @return string
     */
    function decryptIt($encrypted)
    {
        return \Illuminate\Support\Facades\Crypt::decrypt(urldecode($encrypted));
    }
}
/* This will decrypt if the encrypted string is correct otherwise will return whatever the parameter is */
if (! function_exists('safeDecryptIt')) {
    /**
     * @return string
     */
    function safeDecryptIt($encrypted)
    {
        try {

            return \Illuminate\Support\Facades\Crypt::decrypt(urldecode($encrypted));
        } catch (\Exception $e) {
            return $encrypted;
        }
    }
}
if (! function_exists('safeDD')) {
    function safeDD($var)
    {
        if (! app()->environment('production') && ! config('app.live')) {
            dd($var);
        }
        Log::info('debugging:', [$var]);
    }
}

if (! function_exists('versioned_asset')) {
    function versioned_asset($path)
    {
        return config('assets.version') ? asset($path).'?v='.config('assets.version') : asset($path);
    }
}

if (! function_exists('fromMemory')) {
    /* Add value in memory throught out the request response cycle, similar to cache but in memory
    for each request.
    eg: fromMemory('setting_forms', function(){
        return SettingForm::all();
    });

    */
    function fromMemory()
    {
        $args = func_get_args();
        if (count($args) < 2) {
            throw new ApplicationException('Invalid');
        }
        $item = $args[0];
        $callable = $args[1];
        $finalArgs = array_slice($args, 2);
        if (! isset(Helpers::$MEMORY_ITEMS[$item])) {
            Helpers::$MEMORY_ITEMS[$item] = call_user_func_array($callable, (is_array($finalArgs) ? $finalArgs : []));
        }

        return Helpers::$MEMORY_ITEMS[$item];
    }
}

if (! function_exists('jsVar')) {

    function jsVar($data)
    {
        echo '<script type="text/javascript">
	    /* <![CDATA[ */';
        foreach ($data as $k => $v) {
            echo 'window.'.$k.'= '.json_encode($v).';';
        }
        echo '/* ]]> */
	    </script>';
    }
}

if (! function_exists('ajaxSuccess')) {
    function ajaxSuccess($data = [], $message = '')
    {
        return response()->json(array_merge([
            'success' => 1,
            'status' => 'success',
            'message' => $message,
        ], $data));
    }
}
if (! function_exists('ajaxSuccessWithWarning')) {
    function ajaxSuccessWithWarning($data = [], $message = '')
    {
        return response()->json(array_merge([
            'success' => 1,
            'status' => 'warning',
            'message' => $message,
        ], $data));
    }
}
if (! function_exists('ajaxError')) {
    function ajaxError($message = '', $code = 200, $data = [])
    {
        return response()->json([
            'error' => 1,
            'code' => $code,
            'status' => 'error',
            'message' => $message,
            'data' => $data,
        ], $code);
    }
}
if (! function_exists('morphAlias')) {

    /*
    Get the morph key for the class defined with Relation::morphMap in AppServiceProvider
    */
    function morphAlias($class)
    {
        $morphMap = \Illuminate\Database\Eloquent\Relations\Relation::morphMap();
        $flipMorphed = array_flip($morphMap);

        return $flipMorphed[$class] ?? addslashes($class);
    }
}

if (! function_exists('kendify')) {

    /*
    this funtion prepares the data in the form of dataitems applicable for kendo dropdowns and input options
    */
    function kendify($data, $idas = 'id', $textas = 'text')
    {
        $data = array_values(collect($data)->map(function ($item, $key) use ($idas, $textas) {
            return [
                $idas => $key,
                $textas => ! is_array($item) ? html_entity_decode($item) : $item,
            ];
        })->toArray());

        return $data;
    }
}
if (! function_exists('getFeedbackMessages')) {
    /*
    this funtion prepares the data in the form of dataitems applicable for kendo dropdowns and input options
    */
    function getFeedbackMessages($messageKey, $prefix = '', $postfix = '', $parse = [])
    {
        $message = config_locale('messages.'.$messageKey);
        $message = $message ?? 'Undefined error occurred';
        $message = trim(implode(' ', [$prefix, $message, $postfix]));

        return $message;
    }
}
if (! function_exists('abbreviatedDays')) {
    /*
    this function abbreviated the days
    */
    function abbreviatedDays($days)
    {
        $daysAbbreviated = [
            'Monday' => 'Mon',
            'Tuesday' => 'Tue',
            'Wednesday' => 'Wed',
            'Thursday' => 'Thu',
            'Friday' => 'Fri',
        ];

        $abbreviatedDays = [];

        foreach ($days as $day) {
            if (array_key_exists($day, $daysAbbreviated)) {
                $abbreviatedDays[] = $daysAbbreviated[$day];
            }
        }

        return $abbreviatedDays;
    }
}

if (! function_exists('dbRawL10')) {
    function dbRawL10($sql)
    {
        return \Illuminate\Support\Facades\DB::raw($sql)->getValue(\Illuminate\Support\Facades\DB::connection()->getQueryGrammar());
    }
}

if (! function_exists('getStudentApplicationUrl')) {
    function getStudentApplicationUrl()
    {
        // return Config::get('constants.applicationUrl') . base64_encode(tenant()->tenancy_db_name) . '/';
        return Config::get('constants.applicationUrl').tenant()->id.'/'.tenant('domains')[0]['domain'].'/';
        // return Config::get('constants.applicationUrl') . base64_encode(tenant()->tenancy_db_name) . '/' . base64_encode(tenant('domains')[0]['domain']) . '/';
    }
}

if (! function_exists('getStudentApplicationTokenParam')) {
    function getStudentApplicationTokenParam()
    {
        return '?gtoken='.getLoginUserToken();
    }
}

if (! function_exists('getLoginUserToken')) {
    function getLoginUserToken()
    {
        $user = authUser();

        return $user->createToken('auth_token')->plainTextToken;
    }
}

if (! function_exists('hashFileName')) {
    function hashFileName($filename)
    {
        $ext = pathinfo($filename, PATHINFO_EXTENSION);

        return time().'-'.Str::random(16).'-'.Str::random(16).'.'.$ext;
    }
}

if (! function_exists('tempCertificateCondition')) {
    function tempCertificateCondition($studentId, $courseId)
    {
        return tenant()->id == 'axis' && $studentId == '1111' && $courseId == '79';
    }
}

if (! function_exists('fileUrl')) {
    function fileUrl($path, $download = false)
    {
        return route('files.protected-view', [$download ? 'd' : 'p', encryptIt($path), time()]);
    }
}

if (! function_exists('initialLetters')) {
    function initialLetters($fullName)
    {
        $words = explode(' ', $fullName);
        $letter = [];
        foreach ($words as $w) {
            $letter[] = mb_substr($w, 0, 1);
        }

        return implode('', $letter);
    }
}

if (! function_exists('objectToArray')) {
    function objectToArray($d)
    {
        if (is_object($d)) {
            // Gets the properties of the given object
            // with get_object_vars function
            $d = get_object_vars($d);
        }
        if (is_array($d)) {
            /*
            * Return array converted to object
            * Using __FUNCTION__ (Magic constant)
            * for recursive call
            */
            return array_map('objectToArray', $d);
        } else {
            // Return array
            return $d;
        }
    }
}

if (! function_exists('cleanNonUTF')) {
    function cleanNonUTF($str)
    {
        return mb_convert_encoding($str, 'UTF-8', 'UTF-8');
    }
}

function utf8ize($mixed)
{
    // return $mixed;
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return cleanNonUTF($mixed);
    }

    return $mixed;
}

if (! function_exists('mergeFirstArray')) {
    function mergeFirstArray($first, $other)
    {
        foreach ($first as $k => $v) {
            if (isset($other[$k])) {
                $first[$k] = $other[$k];
            }
        }

        return $first;
    }
}
if (! function_exists('parseDate')) {
    function parseDate($date = '', $format = '')
    {
        $returnformat = empty($format) ? 'm/d/Y' : $format;
        if ($date) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $date);
                $invalidDate = Carbon::create(1, 1, 1);
                if ($date->isValid() && $date->greaterThanOrEqualTo($invalidDate)) {
                    if ($format === null) {
                        return $date;
                    }

                    return $date->format($returnformat);
                }
            } catch (\Exception $e) {
                return null;
            }
        }

        return null;
    }
}
if (! function_exists('parseTime')) {
    function parseTime($time = '', $format = '')
    {
        $returnformat = empty($format) ? 'g:i A' : $format;
        if ($time) {
            try {
                $time = Carbon::parse($time);

                return $time->format($returnformat);
            } catch (\Exception $e) {
                return null;
            }
        }

        return null;
    }
}
if (! function_exists('parseDateTime')) {
    function parseDateTime($date = '', $format = '')
    {
        $datestr = trim($date);
        $returnformat = empty($format) ? 'm/d/Y g:i A' : $format;
        if ($datestr) {
            try {
                $date = Carbon::createFromFormat('Y-m-d H:i:s', $datestr);
                $invalidDate = Carbon::create(1, 1, 1);
                if ($date->isValid() && $date->greaterThanOrEqualTo($invalidDate)) {
                    if ($format === null) {
                        return $date;
                    }

                    return $date->format($returnformat);
                }
            } catch (\Exception $e) {
                return null;
            }
        }

        return null;
    }
}
if (! function_exists('calculateHours')) {
    function calculateHours($start_time = '', $end_time = '', $break_from = '', $break_to = '')
    {
        $start_datetime = Carbon::parse($start_time);
        $end_datetime = Carbon::parse($end_time);
        $break_from_datetime = Carbon::parse($break_from);
        $break_to_datetime = Carbon::parse($break_to);
        $total_hours_with_breaks = $start_datetime->diffInHours($end_datetime);
        $break_hours = $break_from_datetime->diffInHours($break_to_datetime);
        $total_hours = $total_hours_with_breaks - $break_hours;

        return $total_hours;
    }
}
if (! function_exists('getPercentage')) {
    function getPercentage($value, $max, $format = true, $getText = false)
    {
        $per = 0;
        if ($max > 0) {
            $per = $value / $max * 100;
        }
        if ($format && ! ($per == 100 || $per == 0)) {
            $per = number_format($per, 2);
        }
        if ($getText) {
            return "$per%";
        }

        return $per;
    }
}

if (! function_exists('galaxy_feature')) {

    function galaxy_feature($key, $onlyTenant = false)
    {

        if (config('features.'.$key) && ! $onlyTenant) {
            return true;
        }

        /* GET CONFIG FROM DB IN THE FUTURE */
        if (config('features.'.$key) && config('features.tenants.'.tenant('id').'.'.$key)) {
            return true;
        }

        return false;
    }
}

function getParsedSQL($model)
{
    $replace = function ($sql, $bindings) {
        $needle = '?';
        foreach ($bindings as $replace) {
            $pos = strpos($sql, $needle);
            if ($pos !== false) {
                if (gettype($replace) === 'string') {
                    $replace = ' "'.addslashes($replace).'" ';
                }
                $sql = substr_replace($sql, $replace, $pos, strlen($needle));
            }
        }

        return $sql;
    };
    $sql = $replace($model->toSql(), $model->getBindings());

    return $sql;
}

if (! function_exists('validate_url')) {
    function validate_url($url)
    {
        $path = parse_url($url, PHP_URL_PATH);
        $encoded_path = array_map('urlencode', explode('/', $path));
        $url = str_replace($path, implode('/', $encoded_path), $url);

        return filter_var($url, FILTER_VALIDATE_URL) ? true : false;
    }
}

if (! function_exists('config_locale')) {
    /* function to ge the localized messages */
    function config_locale($key = null, $defaultMessage = '', $locale = null)
    {
        $messageKey = $key;
        $replaceVals = [];
        if (is_array($key)) {
            [$messageKey, $replaceVals] = $key;
        }
        $message = __($messageKey, $replaceVals, $locale);
        if ($messageKey === $message && $defaultMessage) {
            return $defaultMessage;
        }

        return $message;
    }
}

function checkIsHigherEd()
{
    $courseType = CourseType::where(['college_id' => authUser()->college_id])->select('title', 'status')->get()->toArray();
    $arrSpecialGradeTypeList = Config::get('constants.arrSpecialCourseTypeName');
    $collection = collect($courseType);
    $containsHigherEd = $collection->contains(function ($item) use ($arrSpecialGradeTypeList) {
        return in_array($item['title'], $arrSpecialGradeTypeList) && $item['status'] === 1;
    });

    return $containsHigherEd;
}

if (! function_exists('manageXeroStatus')) {

    function manageXeroStatus($xero_invoice, $isSynced = 0)
    {

        $status = 'Not Synced';
        $icon = 'fa fa-times-circle';

        if (! empty($xero_invoice['xero_synced_at'])) {
            $status = 'Synced';
            $icon = 'fa fa-sync';
        } elseif (! empty($xero_invoice['xero_failed_at'])) {
            $status = 'Sync Failed';
            $icon = 'fa fa-exclamation-triangle';
        } elseif (isset($xero_invoice['xero_data']['purchase_order']) && ! empty($xero_invoice['xero_data']['purchase_order'])) {
            $status = 'PO Created';
            $icon = 'fa fa-sync';
        }

        return '<div class="flex items-center space-x-2"><i class="'.$icon.'"></i><div class="flex items-center">'.$status.'</div></div>';
    }
}
if (! function_exists('manageXeroDateTime')) {

    function manageXeroDateTime($xero_invoice)
    {

        $syncAt = '--';

        if (! empty($xero_invoice['xero_synced_at'])) {
            $syncAt = date('d-m-Y h:i:s A', strtotime($xero_invoice['xero_synced_at']));
        } elseif (! empty($xero_invoice['xero_failed_at'])) {
            $syncAt = date('d-m-Y h:i:s A', strtotime($xero_invoice['xero_failed_at']));
        } elseif (isset($xero_invoice['xero_data']['purchase_order']) && ! empty($xero_invoice['xero_data']['purchase_order'])) {
            $syncAt = date('d-m-Y h:i:s A', strtotime($xero_invoice['created_at']));
        }

        return $syncAt;
    }
}
if (! function_exists('number_to_letters')) {
    function number_to_letters($number)
    {
        $letters = '';
        while ($number > 0) {
            $mod = ($number - 1) % 26;
            $letters = chr(65 + $mod).$letters;
            $number = (int) (($number - $mod) / 26);
        }

        return strtolower($letters);
    }
}

if (! function_exists('generateStatusFilterConditions')) {
    /* this function generates the filter condition to sort the data by the student course status */
    function generateStatusFilterConditions($statusFilters = null)
    {
        $order = config('constants.arrCourseStatusOrder', []);
        if (is_array($statusFilters) && ! empty($statusFilters)) {
            $order = array_merge($statusFilters, $order);
        }
        $query = 'rto_student_courses.status';
        if (! empty($order)) {
            $cnt = 1;
            $cases = [];
            foreach ($order as $val) {
                $orderText = 'aa'.number_to_letters($cnt);
                // append aa just to make sure this string comes first in the order (to make failsafe)
                // $orderText = $cnt;
                $cases[] = 'WHEN rto_student_courses.status = "'.$val.'" THEN "'.$orderText.'"';
                $cnt++;
            }
            $cases[] = 'ELSE rto_student_courses.status';
            $query = 'CASE '.implode(' ', $cases).' END';
        }

        return $query;
    }
}
if (! function_exists('generateOrderByCondition')) {
    /* this function generates the filter condition to sort the data by the student course status */
    function generateOrderByCondition($field = null, $values = null)
    {
        if (! $values) {
            return null;
        }
        if (is_array($field)) {
            $queries = [];
            foreach ($field as $ind => $val) {
                $value = $values[$ind] ?? null;
                $qry = generateOrderByCondition($val, $value);
                if ($qry) {
                    $queries[] = $qry;
                }
            }

            return $queries;
        } else {
            $str = '';
            if (is_array($values)) {
                $checks = [];
                $increment = 1;
                foreach ($values as $condition) {
                    $checks[] = 'WHEN '.$field.' = '.$condition.' THEN '.$increment;
                    $increment++;
                }
                $checks[] = 'ELSE '.$increment;
                $str = '(CASE '.implode(' ', $checks).' END)';
            } else {
                $str = '(CASE WHEN '.$field.' = '.$values.' THEN 1 ELSE 2 END)';
            }

            return $str;
        }
    }
}
if (! function_exists('strToNumber')) {
    function strToNumber($string = '', $split = ',')
    {
        return (float) str_replace($split, '', $string);
    }
}
if (! function_exists('splitTimeDurationString')) {
    function splitTimeDurationString($timeString = '', $infix = ' To ')
    {
        // Format the string as "HH:MM To HH:MM"
        if (! empty($timeString)) {
            $parts = explode(':', $timeString);
            $timesList = [];
            if (count($parts) >= 2) {
                $timesList[] = implode(':', [$parts[0] ?? '00', $parts[1] ?? '00']);
            }
            if (count($parts) > 2) {
                $timesList[] = implode(':', [$parts[2] ?? '00', $parts[3] ?? '00']);
            }

            return implode($infix, $timesList);
        }

        return '';
    }
}
if (! function_exists('setStartAndEndDate')) {
    function setStartAndEndDate($start = null, $end = null, $for = null)
    {
        $hasStart = false;
        try {
            if (is_numeric($start)) {
                if (strlen($start) > 10) {
                    $start = substr($start, 0, -3);
                }
                $start = Carbon::createFromTimestamp((int) $start);
                $hasStart = true;
            } elseif ($start) {
                $start = Carbon::parse($start);
                $hasStart = true;
            }
        } catch (\Exception $e) {
            $start = null;
        }
        try {
            if (is_numeric($end)) {
                if (strlen($end) > 10) {
                    $end = substr($end, 0, -3);
                }
                $end = Carbon::createFromTimestamp((int) $end);
            } elseif ($end) {
                $end = Carbon::parse($end);
            } else {
                $end = Carbon::now();
            }
        } catch (\Exception $e) {
            $end = null;
        }

        if (! $start) {
            $start = Carbon::now()->subMonth()->addDay();
        }
        if (! $end && $for != 'alltime') {
            $for = 'daily';
        }

        if (! $end || $end->lessThan($start)) {
            if (! $hasStart) {
                $start = $end->copy()->subMonth()->addDay();
            } else {
                $end = $start->copy()->addMonth()->subDay();
            }
        }
        if ($start->greaterThan($end)) {
            $start = $end->copy()->subMonth()->addDay();
        }

        switch ($for) {
            case 'day':
            case 'daily':
                $end = $start->copy();
                break;
            case '7days':
                $end = Carbon::now();
                $start = $end->copy()->subDays(6);
                break;
            case 'week':
                if ($start->isMonday() === false) {
                    $start = $start->startOfWeek(Carbon::MONDAY);
                }
                $end = $start->copy()->addDays(6);
                break;
            case 'fortnight':
                $end = $start->copy()->addDays(13);
                break;
            case 'month':
                $end = $start->copy()->addMonth()->subDay();
                break;
            case '3month':
            case 'quarter':
                $start->startOfMonth();
                $end = Carbon::now();
                $start = $end->copy()->subMonth(2)->startOfMonth();
                break;
            case 'year':
                $end = $start->copy()->addMonths(12)->subDay();
                break;
            case '12months':
                $end = Carbon::now();
                $start = $end->copy()->subMonth(11)->startOfMonth();
                break;
            case 'alltime':
                $start = Carbon::parse('2000-01-01');
                $end = Carbon::now();
                $for = 'alltime';
                break;
            default:
                // check if $for is a valid date
                $validDate = parseDate($for, 'Y-m-d');
                if ($validDate) {
                    $for = $validDate;
                    $start = $end = null;
                } else {
                    $for = 'custom';
                }
                break;
        }

        return [
            'start' => ($start) ? $start->toDateString() : null,
            'end' => ($end) ? $end->toDateString() : null,
            'for' => $for,
        ];
    }
}

if (! function_exists('countries_codes')) {
    function countries_codes()
    {
        return Cache::driver('file')->rememberForever('countries_codes', function () {
            return collect(json_decode(file_get_contents(base_path('storage/app/countries.json'))))
                ->map(function ($item) {
                    return [
                        'name' => @$item->country,
                        'short_name' => @$item->short_name,
                        'code' => @$item->flag,
                        'mask' => @$item->phone_mask,
                        'prefix' => @$item->phone_code,
                    ];
                })->toArray();
        });
    }
}

if (! function_exists('parseLogText')) {
    function parseLogText($text = '')
    {
        $result = [
            'log_text' => null,
            'send_to' => null,
            'from_email' => null,
            'subject' => null,
            'letter_content' => null,
            'attachments' => [],
        ];

        $result['log_text'] = $text ?? null;

        // Extract "Send to"
        preg_match('/Send to\s*:\s*(.*?)<br>/', $text, $sendToMatch);
        $text = str_replace($sendToMatch[0] ?? '', '', $text);
        $result['send_to'] = $sendToMatch[1] ?? null;

        // Extract "From email"
        preg_match('/From email\s*:\s*(.*?)<br>/', $text, $fromEmailMatch);
        $text = str_replace($fromEmailMatch[0] ?? '', '', $text);
        $result['from_email'] = $fromEmailMatch[1] ?? null;

        // Extract "Subject"
        preg_match('/Subject\s*:\s*(.*?)<br>/', $text, $subjectMatch);
        $text = str_replace($subjectMatch[0] ?? '', '', $text);
        $result['subject'] = $subjectMatch[1] ?? null;

        // Extract "Letter content"
        preg_match('/<br>\s*(.*?)Attachments\s*:/s', $text, $letterContentMatch);
        $text = str_replace($letterContentMatch[0] ?? '', '', $text);
        $result['letter_content'] = isset($letterContentMatch[1]) ? trim($letterContentMatch[1]) : trim($text);

        // Extract "Attachments"
        preg_match_all('/<a[^>]+href=[\'"]([^\'"]+)[\'"][^>]*title=[\'"]([^\'"]+)[\'"][^>]*>.*?<\/a>/', $text, $attachmentMatches, PREG_SET_ORDER);
        foreach ($attachmentMatches as $attachment) {
            $result['attachments'][] = [
                'url' => $attachment[1],
                'file_name' => $attachment[2],
                'file_type' => pathinfo($attachment[2], PATHINFO_EXTENSION),
            ];
        }

        return $result;
    }
}
if (! function_exists('prepareCommunicationLogContent')) {
    function prepareCommunicationLogContent($to = null, $from = null, $subject = null, $body = null, $attachments = null, $ccbcc = null)
    {
        $messageArrays = [];
        if ($to) {
            $messageArrays[] = " Send to : {$to}";
        }
        if ($from) {
            $messageArrays[] = " From email : {$from}";
        }
        if ($ccbcc) {
            if ($ccbcc['cc']) {
                $cc = is_array($ccbcc['cc']) ? implode(',', $ccbcc['cc']) : $ccbcc['cc'];
                $messageArrays[] = " CC : {$cc}";
            }
            if ($ccbcc['bcc']) {
                $bcc = is_array($ccbcc['bcc']) ? implode(',', $ccbcc['bcc']) : $ccbcc['bcc'];
                $messageArrays[] = " BCC : {$bcc}";
            }
        }
        if ($subject) {
            $messageArrays[] = " Subject : {$subject}";
        }
        if ($body) {
            $messageArrays[] = $body;
        }
        if ($attachments) {
            $attachments = is_array($attachments) ? implode('', $attachments) : $attachments;
            $messageArrays[] = "{$attachments}";
        }

        return implode('<br>', $messageArrays);
    }
}

if (! function_exists('galaxy_log_to_file')) {
    function galaxy_log_to_file($log = '', $context = [], $file = 'laravel', $current = 'laravel')
    {
        if ($log) {
            $file = str_replace('.log', '', $file).'.log';
            $current = str_replace('.log', '', $current).'.log';

            Log::build([
                'driver' => 'single',
                'path' => base_path('storage/logs/'.$file),
            ])->info($log, array_merge([$context], [
                'logged_in_user' => auth()->user() ? auth()->user()->email : '',
                'tenant' => tenant('id'),
            ]));
        }
    }
}

if (! function_exists('getCentralMeta')) {
    function getCentralMeta($metaIndex = '')
    {
        $domains = tenant()->getMeta($metaIndex);
        $domains = explode(',', $domains);
        foreach ($domains as $ind => $val) {
            $cleanValue = preg_match('/\[(.*?)\]/', $val, $matches);
            $domains[$ind] = $matches[1] ?? null;
        }
        $domains = implode(',', $domains);

        return $domains;
        // try{
        //     if(empty($metaIndex)) return "";
        //     $currentTenantId = tenant("id");
        //     //$centralData = Tenant::where('id', $currentTenantId)->first();
        //     //$domains = ($centralData) ? $centralData->getMeta($metaIndex) : '';
        //     $domains = tenancy()->central(function () use ($currentTenantId, $metaIndex) {
        //         $centralData = Tenant::where('id', $currentTenantId)->first();
        //         $metaval = ($centralData) ? $centralData->getMeta($metaIndex) : '';
        //         return $metaval;
        //     });
        //     tenancy()->initialize($currentTenantId);
        //     // $currentTenantId = tenant();
        //     // dd(vars: $currentTenantId);
        // } catch (Exception $e){
        //     $domains = "";
        // }
        // return $domains;
    }
}

if (! function_exists('modifyUrl')) {
    function modifyUrl($url)
    {
        // Remove 'https://' or 'http://'
        $url = preg_replace('/^https?:\/\//', '', $url);

        // Ensure 'www.' is present
        if (! preg_match('/^www\./', $url)) {
            $url = 'www.'.$url;
        }

        // Remove trailing slash if present
        $url = rtrim($url, '/');

        return $url;
    }
}

if (! function_exists('validateRedirectUri')) {
    function validateRedirectUri($redirectUri = '', $getDefault = true)
    {
        $validUrl = false;
        $hostUrl = null;
        if (! empty($redirectUri)) {

            $hostUrl = $redirectUri;
            if (! preg_match('/^https?:\/\//', $hostUrl)) {
                $hostUrl = 'http://'.$redirectUri;
            }

            $parsedUrl = parse_url($hostUrl);

            $host = $parsedUrl['host'] ?? null;

            $tenant = ($host) ? Tenant::GetTenantInfoByAllowedDomain($host) : null;
            $url = ($tenant) ? $tenant->GetApiBaseurl() : null;

            if ($url) {
                $validUrl = true;
            }
        }

        if ($validUrl) {
            return $hostUrl;
        } elseif ($getDefault) {
            /* get the default redirect uri */
            $currentTenant = tenant();
            $tenantDomain = $currentTenant->domain ?? $currentTenant->id ?? null;

            $shortcourseDomain = config('app.shortcourse_domain');

            return $tenantDomain.$shortcourseDomain;
        }

        return null;
    }
}

/* tenant logo cached or galaxy logo */
if (! function_exists('tenant_app_logo')) {
    function tenant_app_logo()
    {
        /* check if we already have the logo in cache to avoid db call */
        $cachedKey = tenant('id').SiteConstants::LOGO_CACHE_KEY;
        $logo = Cache::driver('file')->get($cachedKey);
        if ($logo) {
            return $logo;
        }

        $data = \App\Model\v2\Colleges::orderByDesc('status')->orderByDesc('id')->first();
        $filePath = config('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath, null, $data['id']);

        $logo_picture_path = $destinationPath['view'].$data['college_logo'];

        return \Support\Services\UploadService::cacheItem($logo_picture_path, SiteConstants::LOGO_CACHE_KEY ?? 'tenant_logo');
    }
}

// tenant getCurrentRole
if (! function_exists('getCurrentRole')) {
    function getCurrentRole()
    {
        $roleId = Session::get('currentRole');
        if (! $roleId) {
            return null;
        }

        return \App\Model\v2\Roles::find($roleId);
    }
}

// tenant get Current Staff
if (! function_exists('getCurrentStaff')) {
    function getCurrentStaff()
    {
        $authUser = Auth::user();
        if (! $authUser) {
            return null;
        }
        $staffInfo = Staff::where('college_id', $authUser?->college_id)
            ->where('user_id', $authUser->id)
            ->select('id')
            ->first();

        return $staffInfo;
    }
}
