<?php

namespace App\Model;

use Auth;
use Config;
use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    //
    protected $table = 'rto_country';

    protected $fillable = ['id', 'college_id', 'name', 'countrycode', 'countrylevel', 'nationality', 'region', 'absvalue', 'status', 'created_by', 'updated_by'];

    public function getCountryList($collegeId)
    {
        // $collegeId = Auth::user()->college_id;
        $arrCountry = Country::where('college_id', '=', $collegeId)->orwhere('college_id', '=', 0)->orderby('name', 'asc')->pluck('name', 'id')->toArray();
        $countryRecord[''] = '- - - Select Country - - -';

        return $returnCountryRecord = $countryRecord + $arrCountry;
    }

    public function getCountryListV2($collegeId)
    {
        return Country::where('college_id', '=', $collegeId)->orwhere('college_id', '=', 0)->orderby('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function getNationalityList()
    {

        $collegeId = Auth::user()->college_id;
        $arrNationality = Country::where('college_id', '=', $collegeId)->orwhere('college_id', '=', 0)->orderby('nationality', 'asc')->pluck('nationality', 'id')->toArray();
        $nationalityRecord[''] = '- - - Select Nationality - - -';

        return $returnNationalityRecord = $nationalityRecord + $arrNationality;
    }

    public function getNationalityListV2($collegeId, $userId = '')
    {

        $arrNationality = Country::where('college_id', '=', $collegeId)->orwhere('college_id', '=', 0)->orderby('nationality', 'asc')->pluck('nationality', 'id')->toArray();
        $nationalityRecord[''] = '- - - Select Nationality - - -';

        return $returnNationalityRecord = $nationalityRecord + $arrNationality;
    }

    public function getCountryId($countryname)
    {

        echo $data = Country::where('name', $countryname)->get(['id', 'name']);
    }

    public function getSelectedCountry($country)
    {
        return Country::where('id', '=', $country)->get(['name']);
    }

    public function countryAdd($request)
    {
        $college_id = Auth::user()->college_id;
        $collageArr = [0, $college_id];
        $countryCount = Country::whereIn('college_id', $collageArr)->where('name', '=', $request->input('name'))->count();
        $nationalityCount = Country::whereIn('college_id', $collageArr)->where('nationality', '=', $request->input('nationality'))->count();

        if ($countryCount == 0 && $nationalityCount == 0) {
            $objCountry = new Country;
            $objCountry->college_id = $college_id;
            $objCountry->name = ($request->input('name') != '') ? $request->input('name') : null;
            $objCountry->countrycode = ($request->input('countrycode') != '') ? $request->input('countrycode') : null;
            $objCountry->countrylevel = ($request->input('countrylevel') != '') ? $request->input('countrylevel') : null;
            $objCountry->nationality = ($request->input('nationality') != '') ? $request->input('nationality') : null;
            $objCountry->region = ($request->input('region') != '') ? $request->input('region') : null;
            $objCountry->absvalue = ($request->input('absvalue') != '') ? $request->input('absvalue') : null;
            $objCountry->created_by = Auth::user()->id;
            $objCountry->updated_by = Auth::user()->id;
            $objCountry->save();
            $returnData['type'] = 'session_success';
            $returnData['message'] = 'Country Saved Successfully.';

            return $returnData;
        } else {
            $msg = ($countryCount == 1) ? 'Country name Already Exist' : 'Nationality name Already Exist';
            $returnData['type'] = 'session_error';
            $returnData['message'] = $msg;

            return $returnData;
        }
    }

    public function getCountryData($college_id, $perPage)
    {
        $arrCountry = Country::where('college_id', $college_id)
            ->orwhere('college_id', '=', 0)
            ->orderBy('name', 'asc')
            ->paginate($perPage);

        return $arrCountry;
    }

    public function countryDelete($countryId)
    {

        return Country::where('id', '=', $countryId)->delete();
    }

    public function countryLevelUpdate($primaryId, $countryLevel)
    {
        $objCountry = Country::find($primaryId);
        $objCountry->countrylevel = $countryLevel;
        $objCountry->updated_by = Auth::user()->id;
        $objCountry->save();
    }

    public function countryEdit($request)
    {
        $countryId = $request->id;

        $college_id = Auth::user()->college_id;
        $collageArr = [0, $college_id];
        $countryCount = Country::whereIn('college_id', $collageArr)
            ->where('name', '=', $request->input('name'))
            ->where('id', '!=', $countryId)
            ->count();
        echo $countryCount;
        $nationalityCount = Country::whereIn('college_id', $collageArr)
            ->where('nationality', '=', $request->input('nationality'))
            ->where('id', '!=', $countryId)
            ->count();

        if ($countryCount == 0 && $nationalityCount == 0) {
            $objCountry = Country::find($countryId);
            $objCountry->college_id = $college_id;
            $objCountry->name = ($request->input('name') != '') ? $request->input('name') : null;
            $objCountry->countrycode = ($request->input('countrycode') != '') ? $request->input('countrycode') : null;
            $objCountry->countrylevel = ($request->input('countrylevel') != '') ? $request->input('countrylevel') : null;
            $objCountry->nationality = ($request->input('nationality') != '') ? $request->input('nationality') : null;
            $objCountry->region = ($request->input('region') != '') ? $request->input('region') : null;
            $objCountry->absvalue = ($request->input('absvalue') != '') ? $request->input('absvalue') : null;
            $objCountry->created_by = Auth::user()->id;
            $objCountry->updated_by = Auth::user()->id;
            $objCountry->save();
            $returnData['type'] = 'session_success';
            $returnData['message'] = 'Country Updated Successfully.';

            return $returnData;
        } else {
            $msg = ($countryCount == 1) ? 'Country name Already Exist' : 'Nationality name Already Exist';
            $returnData['type'] = 'session_error';
            $returnData['message'] = $msg;

            return $returnData;
        }
    }

    public function getCountrylistCollegeIdWise($countryId)
    {
        $data = Country::where('id', $countryId)->get();

        return $data;
    }

    public function countryLevelDetect($countryLevelModal)
    {
        $collegeId = Auth::user()->college_id;
        $data = Country::where('college_id', '=', $collegeId)
            ->where('countrylevel', $countryLevelModal)->get();

        return $data;
    }

    public function getCountryFilterData($filterOption, $filterText, $filterLevel, $filterRegion)
    {
        $collegeId = Auth::user()->college_id;
        $data = Country::whereIn('college_id', [0, $collegeId]);

        if ($filterOption == 'absvalue' || $filterOption == 'name') {
            $data->where($filterOption, $filterText);
        }
        if ($filterOption == 'countrylevel') {
            $data->where($filterOption, $filterLevel);
        }
        if ($filterOption == 'region') {
            $data->where($filterOption, $filterRegion);
        }

        return $res = $data->get()->toarray();
    }

    public function getCountryAbsList()
    {
        $collegeId = Auth::user()->college_id;
        $arrCountry = Country::where('college_id', '=', $collegeId)->orwhere('college_id', '=', 0)->pluck('name', 'absvalue')->toArray();
        $countryRecord[''] = '- - - Select Country - - -';

        return $returnCountryRecord = $countryRecord + $arrCountry;
    }

    /* Start test case for common-table */

    public function getCountryDataTestCase($request)
    {

        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'id',
            1 => 'name',
            2 => 'nationality',
            3 => 'absvalue',
            4 => 'countrylevel',
            5 => 'countrycode',
            6 => 'region',
            7 => 'status',
        ];

        $query = Country::from('rto_country')->select('id', 'name', 'countrycode', 'countrylevel', 'nationality', 'region', 'absvalue', 'status');
        $totalData = $query->count();

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];

            $query->where(function ($query) use ($searchVal) {

                $query->where('id', 'like', '%'.$searchVal.'%');
                $query->orWhere('name', 'like', '%'.$searchVal.'%');
                $query->orWhere('nationality', 'like', '%'.$searchVal.'%');
                $query->orWhere('absvalue', 'like', '%'.$searchVal.'%');
                $query->orWhere('countrylevel', 'like', '%'.$searchVal.'%');
                $query->orWhere('countrycode', 'like', '%'.$searchVal.'%');
                $query->orWhere('region', 'like', '%'.$searchVal.'%');
                $query->orWhere('status', 'like', '%'.$searchVal.'%');
            });
        }

        $totalFiltered = $query->count();

        $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);
        $query->skip($requestData['start'])->take($requestData['length']);
        $resultArr = $query->get();

        $data = [];
        foreach ($resultArr as $row) {

            $nestedData = [];
            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                <li>
                                    <span data-toggle="modal" class="delete" data-id="'.$row['id'].'" data-target="#deleteModal"> 
                                        <a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i> </a> 
                                    </span>
                                </li>
                                <li>
                                    <a href="javascript:;"  class="edit-college-marterials link-black text-sm" data-toggle="tooltip" data-original-title="Edit" id="'.$row['id'].'" ><i class="fa fa-edit"></i></a> 
                                </li>
                            </ul>
                        </div>';

            $nestedData[] = $row['id'].$action;
            $nestedData[] = $row['name'];
            $nestedData[] = $row['nationality'];
            $nestedData[] = $row['absvalue'];
            $nestedData[] = $row['countrylevel'];
            $nestedData[] = $row['countrycode'];
            $nestedData[] = $row['region'];
            $nestedData[] = $row['status'];
            $data[] = $nestedData;
        }
        // echo "<pre>";print_r($data);exit;

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    /* End test case for common-table */

    public function getCountryDatatableData($request, $collegeId)
    {

        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'rc.name',
            1 => 'rc.nationality',
            2 => 'rc.countrylevel',
            3 => 'rc.countrycode',
            4 => 'rc.region',
            5 => 'rc.absvalue',
        ];

        $arrRegion = Config::get('constants.region');
        $query = Country::from('rto_country as rc')->whereIn('college_id', [0, $collegeId]);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData, $arrRegion) {
                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 4) {
                        $results = array_filter($arrRegion, function ($value) use ($searchVal) {
                            return strpos(strtolower($value), strtolower($searchVal)) !== false;
                        });
                        $results = array_keys($results);
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } elseif ($key == 4) {
                            $query->orWhere(function ($subquery) use ($value, $results) {
                                $subquery->whereIn($value, $results);
                            });
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select(
                'rc.name',
                'rc.countrycode',
                'rc.countrylevel',
                'rc.college_id',
                'rc.nationality',
                'rc.region',
                'rc.absvalue',
                'rc.status',
                'rc.id'
            )->get();

        $data = [];
        foreach ($resultArr as $row) {

            $nestedData = [];
            $action = '';
            if ($row['college_id'] != 0) {
                $actionHtml = '';
                $actionHtml .= '<li><a href="'.route('edit-country', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit"><i class="fa fa-edit"></i></a></li>';
                $actionHtml .= '<li><span data-toggle="modal" class="delete" data-id="'.$row['id'].'" data-target="#deleteModal"> 
                                <a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a></span></li>';
                $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                              '.$actionHtml.'
                            </ul>
                        </div>';
            }

            $nestedData[] = $row['name'].$action;
            $nestedData[] = $row['nationality'];
            $nestedData[] = (empty($row['countrylevel']) ? '-' : $row['countrylevel']);
            $nestedData[] = (empty($row['countrycode']) ? '-' : $row['countrycode']);
            $nestedData[] = (empty($row['region']) ? '-' : $arrRegion[$row['region']]);
            $nestedData[] = (empty($row['absvalue']) ? '-' : $row['absvalue']);
            $data[] = $nestedData;
        }
        //        echo "<pre>";print_r($data);exit;

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function getCountryListWithAbs($collegeId)
    {
        return Country::where('college_id', '=', $collegeId)
            ->orwhere('college_id', '=', 0)
            ->orderby('name', 'asc')
            ->pluck('name', 'absvalue')
            ->toArray();
    }

    public function scopeFilterQuery(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('name', 'like', "%$value%");
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value)->orWhere('college_id', 0);
    }
}
