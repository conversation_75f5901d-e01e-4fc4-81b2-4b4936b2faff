<?php

namespace App\Model;

use App\Traits\AvetMissTrait;
use Auth;
use DB;
use Illuminate\Database\Eloquent\Model;

class Courses extends Model
{
    protected $table = 'rto_courses';

    use AvetMissTrait;

    public function CourseAdd($request, $college_id, $loginUserId)
    {

        // echo $request->input('is_superseded');exit;
        $courses = new Courses;
        $courses->college_id = $college_id;
        $courses->created_by = $loginUserId;
        $courses->updated_by = $loginUserId;

        $is_higher_edu = $request->input('is_higher_education');
        $course_type_id = $request->input('course_type');
        $courses->course_type_id = $course_type_id;
        if ($course_type_id == config('constants.vet_course_type_id')) {
            $courses->module_delivery = $request->input('module_delivery');
            $is_superseded = $request->input('is_superseded');
            if ($is_superseded == 1) {
                $courses->superseded_date = (! empty($request->input('superseded_date'))) ? date('Y-m-d', strtotime($request->input('superseded_date'))) : null;
            } else {
                $courses->superseded_date = null;
            }
            $courses->is_superseded = $is_superseded;
            $courses->fee_help = $request->input('fee_help');
            if ($request->input('fee_help') == 1) {
                $courses->free_help_study_type = $request->input('free_help_study_type');
                $courses->free_help_study_load = $request->input('free_help_study_load');
            } else {
                $courses->free_help_study_type = null;
                $courses->free_help_study_load = null;
            }
            $courses->AVETMISS_Report = $request->input('AVETMISS_Report');
            $courses_duration_type = $request->input('course_duration_type');
        }
        if ($course_type_id == 1) {
            $courses->module_delivery = null;
            $courses->is_superseded = null;
            $courses->is_super_code = null;
            $courses->fee_help = null;
            $courses->free_help_study_type = null;
            $courses->free_help_study_load = null;
            $courses->AVETMISS_Report = null;
        }
        $courses->national_code = $request->input('nation_code');
        $courses->course_code = $request->input('course_code');

        if ($is_higher_edu) {
            $courses->cricos_code = $request->input('cricos_code');
        } else {
            if ($request->input('module_delivery') == 1) {
                // $courses->cricos_code = null;
            } else {
                $courses->cricos_code = $request->input('cricos_code');
            }
        }

        $courses->course_name = $request->input('course_name');
        $courses->course_duration = $request->input('course_duration');
        if ($course_type_id == 1) {
            $courses_duration_type = 2;
        } else {
            $courses_duration_type = $request->input('course_duration_type');
        }
        $courses->couse_duration_type = $courses_duration_type;
        $delivery_target = $request->input('delivery_target');
        if ($delivery_target == 'International') {
            $courses->domestic_fee = null;
        } else {
            $domestic = $request->input('domestictuition_fee');
            if ($domestic == '') {
                $courses->domestic_fee = null;
            } else {
                $courses->domestic_fee = $request->input('domestictuition_fee');
            }
        }
        $courses->delivery_target = $delivery_target;
        $courses->tuition_fee = $request->input('tuition_fee');
        $courses->maximum_weekly_study = $request->input('maximum_weekly_study');
        $courses->face_to_face_hours = $request->input('face_to_face_hours');
        $courses->online_hours = $request->input('online_hours');
        $courses->qualification_prerequisite_id = (! empty($request->input('qualification_prerequisite'))) ? $request->input('qualification_prerequisite') : 'None';
        $courses->faculty_id = ($request->input('faculty') != '') ? $request->input('faculty') : '';
        $courses->department_id = ($request->input('department') != '') ? $request->input('department') : '';
        $courses->major = ($request->input('major') != '') ? $request->input('major') : '';
        $courses->course_recognition_id = $request->input('course_recognition');
        $courses->level_of_education_id = $request->input('level_of_education');
        $courses->field_of_education_id = $request->input('field_of_education');
        $courses->ANZSCO_code = $request->input('ANZSCO_code');
        $courses->total_nominal_hours = $request->input('total_nominal_hours');
        $campus_lists = $request->input('campusList');
        if (! empty($campus_lists)) {
            $campus_list = implode(',', $campus_lists);
            $courses->campus_list = $campus_list;
        } else {
            $campus_list = '';
            $courses->campus_list = $campus_list;
        }
        $courses->results_calculation_methods = $request->input('results_calculation_methods');
        $work_placement_radio = $request->input('work_placement_radio');
        if ($work_placement_radio == 1) {
            $courses->work_placement_hour = $request->input('work_placement_hour');
            $courses->placement_officer_id = $request->input('placement_officer_id');
            $courses->vocational_startweek = $request->input('vocational_startweek');
            $courses->vocational_duration = $request->input('vocational_duration');
        }
        if ($work_placement_radio == 0) {
            $courses->placement_officer_id = null;
            $courses->work_placement_hour = null;
            $courses->vocational_startweek = null;
            $courses->vocational_duration = null;
        }
        $courses->work_placement = $work_placement_radio;
        if ($request->input('course_level_radio_hidden') == 0) {
            $courses->couse_level = $request->input('course_level_radio_hidden');
        } else {
            $courses->couse_level = $request->input('course_level_radio');
        }
        $active_now = $request->input('Activate_radio');
        if (empty($active_now)) {
            $courses->activated_now = 0;
        } else {
            $courses->activated_now = $active_now;
        }
        $courses->flexible_attendance = $request->input('Flexible_Attendance_Radio');

        // $courses->save();
        // $LastCourseId = $courses->id;

        if ($courses->save()) {
            $objCourseMaterial = new CourseMaterial;
            $objCourseMaterial->createFolderCourseName($college_id, $loginUserId, $courses->course_code, $courses->course_name);
            $LastCourseId = $courses->id;
        }

        for ($i = 0; $i < count($campus_lists); $i++) {
            $coursesCampus = new CollegeCourse;
            $campus_id = $campus_lists[$i];
            $coursesCampus->college_id = $college_id;
            $coursesCampus->course_id = $LastCourseId;
            $coursesCampus->campus_id = $campus_id;
            $coursesCampus->save();
        }
    }

    public function CourseAddV2($request, $college_id, $loginUserId)
    {

        $courses = new Courses;
        $courses->college_id = $college_id;
        $courses->created_by = $loginUserId;
        $courses->updated_by = $loginUserId;
        $courses->module_delivery = null;
        $courses->is_superseded = null;
        $courses->is_super_code = null;

        $feeHelp = $request->input('fee_help');
        $courses->fee_help = $feeHelp;
        $courses->free_help_study_type = ($feeHelp == 1) ? $request->input('free_help_study_type') : null;
        $courses->free_help_study_load = ($feeHelp == 1) ? $request->input('free_help_study_load') : null;

        $courses->course_type_id = $request->input('course_type');
        $courses->national_code = $request->input('nation_code');
        $courses->course_code = $request->input('course_code');
        $courses->course_name = $request->input('course_name');
        $courses->course_duration = $request->input('course_duration');
        $courses->couse_duration_type = $request->input('course_duration_type');

        $is_cricos_code = $request->input('is_cricos_code');
        if (isset($is_cricos_code) && $is_cricos_code == '1') {
            $courses->cricos_code = $request->input('cricos_code');
        } else {
            $courses->cricos_code = null;
        }
        $courses->is_cricos_code = $request->input('is_cricos_code');

        $delivery_target = $request->input('delivery_target');
        if ($delivery_target == 'International') {
            $domesticFee = null;
        } else {
            $domestic = $request->input('domestictuition_fee');
            if ($domestic == '') {
                $domesticFee = null;
            } else {
                $domesticFee = $request->input('domestictuition_fee');
            }
        }

        $courses->domestic_fee = $domesticFee;
        $courses->delivery_target = $delivery_target;
        $courses->tuition_fee = ($request->input('tuition_fee') != '') ? $request->input('tuition_fee') : 0;
        $courses->maximum_weekly_study = $request->input('maximum_weekly_study');
        $courses->qualification_prerequisite_id = (! empty($request->input('qualification_prerequisite'))) ? $request->input('qualification_prerequisite') : 'None';
        $courses->faculty_id = ($request->input('faculty') != '') ? $request->input('faculty') : '';
        $courses->department_id = ($request->input('department') != '') ? $request->input('department') : '';
        $courses->major = ($request->input('major') != '') ? $request->input('major') : '';

        $campus_lists = $request->input('campusList');
        if (! empty($campus_lists)) {
            $campus_list = implode(',', $campus_lists);
            $courses->campus_list = $campus_list;
        } else {
            $campus_list = '';
            $courses->campus_list = $campus_list;
        }

        $courses->results_calculation_methods = $request->input('results_calculation_methods');
        $work_placement_radio = $request->input('work_placement_radio');
        $courses->work_placement = $work_placement_radio;
        $courses->work_placement_hour = ($work_placement_radio == 1) ? $request->input('work_placement_hour') : null;
        $courses->placement_officer_id = ($work_placement_radio == 1) ? $request->input('placement_officer_id') : null;
        $courses->vocational_startweek = ($work_placement_radio == 1) ? $request->input('vocational_startweek') : null;
        $courses->vocational_duration = ($work_placement_radio == 1) ? $request->input('vocational_duration') : null;

        if ($request->input('course_level_radio_hidden') == 0) {
            $courses->couse_level = $request->input('course_level_radio_hidden');
        } else {
            $courses->couse_level = $request->input('course_level_radio');
        }
        $courses->activated_now = (empty($request->input('Activate_radio'))) ? 0 : $request->input('Activate_radio');
        $courses->flexible_attendance = $request->input('Flexible_Attendance_Radio');
        $courses->course_delivery_type = $request->input('course_delivery_type');
        $courses->effective_start = (! empty(($request->input('effective_start')))) ? date('Y-m-d', strtotime($request->input('effective_start'))) : null;
        $courses->effective_end = (! empty(($request->input('effective_end')))) ? date('Y-m-d', strtotime($request->input('effective_end'))) : null;
        $courses->is_tcsi = $request->input('is_tcsi');
        $courses->course_completion_type = $request->input('course_completion_type');
        $courses->course_credit_point = $request->input('course_credit_point');
        $courses->qtac_course_id = $request->input('qtac_course_id');
        $courses->course_description = $request->input('course_description');

        // $courses->save();
        // $LastCourseId = $courses->id;
        // dd($LastCourseId);

        if ($courses->save()) {
            $objCourseMaterial = new CourseMaterial;
            $objCourseMaterial->createFolderCourseName($college_id, $loginUserId, $courses->course_code, $courses->course_name);
            $LastCourseId = $courses->id;
        }

        for ($i = 0; $i < count($campus_lists); $i++) {
            $coursesCampus = new CollegeCourse;
            $coursesCampus->college_id = $college_id;
            $coursesCampus->course_id = $LastCourseId;
            $coursesCampus->campus_id = $campus_lists[$i];
            $coursesCampus->save();
        }
    }

    public function courseEdit($request, $college_id, $id, $loginUserId)
    {
        $courses = Courses::find($id);
        $course_type_id = $request->input('course_type_id');
        // $courses->course_type_id = $course_type_id;
        $courses->updated_by = $loginUserId;

        if ($course_type_id == config('constants.vet_course_type_id')) {
            $courses->module_delivery = $request->input('module_delivery');
            $is_superseded = $request->input('is_superseded');
            if ($is_superseded == 1) {
                $courses->is_super_code = $request->input('is_super_code');
            } else {
                $courses->is_super_code = null;
            }
            $courses->is_superseded = $is_superseded;
            $courses->fee_help = $request->input('fee_help');
            if ($request->input('fee_help') == 1) {
                $courses->free_help_study_type = $request->input('free_help_study_type');
                $courses->free_help_study_load = $request->input('free_help_study_load');
            } else {
                $courses->free_help_study_type = null;
                $courses->free_help_study_load = null;
            }
            $courses->AVETMISS_Report = $request->input('AVETMISS_Report');
            $courses_duration_type = $request->input('course_duration_type');
        }
        if ($course_type_id == 1) {
            $courses->module_delivery = null;
            $courses->is_superseded = null;
            $courses->is_super_code = null;
            $courses->fee_help = null;
            $courses->free_help_study_type = null;
            $courses->free_help_study_load = null;
            $courses->AVETMISS_Report = null;
        }

        if ($request->input('course_level_radio_hidden') == 0) {
            $courses->couse_level = $request->input('course_level_radio_hidden');
        } else {
            $courses->couse_level = $request->input('course_level_radio');
        }
        $courses->national_code = $request->input('nation_code');
        $courses->course_code = $request->input('course_code');

        $is_module_delivery = $request->input('module_delivery');
        if ($is_module_delivery == 1) {
            $courses->cricos_code = null;
        } else {
            $courses->cricos_code = $request->input('cricos_code');
        }
        $courses->course_name = $request->input('course_name');
        $courses->course_duration = $request->input('course_duration');

        if ($course_type_id == 1) {
            $courses_duration_type = 2;
        } else {
            $courses_duration_type = $request->input('course_duration_type');
        }
        $courses->couse_duration_type = $courses_duration_type;
        $delivery_target = $request->input('delivery_target');
        if ($delivery_target == 'International') {
            $courses->domestic_fee = null;
        } else {
            $domestic = $request->input('domestictuition_fee');
            if ($domestic == '') {
                $courses->domestic_fee = null;
            } else {
                $courses->domestic_fee = $request->input('domestictuition_fee');
            }
        }
        $courses->delivery_target = $delivery_target;
        $courses->tuition_fee = $request->input('tuition_fee');
        $courses->maximum_weekly_study = $request->input('maximum_weekly_study');
        $courses->face_to_face_hours = $request->input('face_to_face_hours');
        $courses->online_hours = $request->input('online_hours');
        $courses->qualification_prerequisite_id = (! empty($request->input('qualification_prerequisite'))) ? $request->input('qualification_prerequisite') : 'None';
        $courses->faculty_id = $request->input('faculty');
        $courses->department_id = $request->input('department');
        $courses->major = $request->input('major');
        $courses->course_recognition_id = $request->input('course_recognition');
        $courses->level_of_education_id = $request->input('level_of_education');
        $courses->field_of_education_id = $request->input('field_of_education');
        $courses->ANZSCO_code = $request->input('ANZSCO_code');
        $courses->total_nominal_hours = $request->input('total_nominal_hours');
        $campus_lists = $request->input('campusList');
        if (! empty($campus_lists)) {
            $campus_list = implode(',', $campus_lists);
            $courses->campus_list = $campus_list;
        } else {
            $campus_list = '';
        }
        $courses->results_calculation_methods = $request->input('results_calculation_methods');
        $work_placement_radio = $request->input('work_placement_radio');
        if ($work_placement_radio == 1) {
            $courses->work_placement_hour = $request->input('work_placement_hour');
            $courses->placement_officer_id = $request->input('placement_officer_id');
            $courses->vocational_startweek = $request->input('vocational_startweek');
            $courses->vocational_duration = $request->input('vocational_duration');
        }
        if ($work_placement_radio == 0) {
            $courses->work_placement_hour = null;
            $courses->placement_officer_id = null;
            $courses->vocational_startweek = null;
            $courses->vocational_duration = null;
        }
        $courses->work_placement = $work_placement_radio;
        // $courses->couse_level = $request->input('course_level_radio');
        $active_now = $request->input('Activate_radio');
        if (empty($active_now)) {
            $courses->activated_now = 0;
        } else {
            $courses->activated_now = $active_now;
        }
        $courses->flexible_attendance = $request->input('Flexible_Attendance_Radio');
        $courses->save();
        $LastCourseId = $courses->id;
        $deleteCourseCampus = DB::table('rto_course_campus')
            ->Where('course_id', $id)
            ->Where('college_id', $college_id)
            ->delete();
        for ($i = 0; $i < count($campus_lists); $i++) {
            $coursesCampus = new CollegeCourse;
            $campus_id = $campus_lists[$i];
            $coursesCampus->college_id = $college_id;
            $coursesCampus->course_id = $LastCourseId;
            $coursesCampus->campus_id = $campus_id;
            $coursesCampus->save();
        }
    }

    public function courseEditV2($request, $college_id, $id, $loginUserId)
    {

        $courses = Courses::find($id);

        $course_type_id = $request->input('course_type');
        // $courses->course_type_id = $course_type_id;

        $courses->updated_by = $loginUserId;
        $courses->national_code = $request->input('nation_code');
        $courses->course_code = $request->input('course_code');
        $courses->cricos_code = $request->input('cricos_code');
        $courses->course_name = $request->input('course_name');
        $courses->course_duration = $request->input('course_duration');
        $courses->couse_duration_type = $request->input('course_duration_type');

        $feeHelp = $request->input('fee_help');
        $courses->fee_help = $feeHelp;
        $courses->free_help_study_type = ($feeHelp == 1) ? $request->input('free_help_study_type') : null;
        $courses->free_help_study_load = ($feeHelp == 1) ? $request->input('free_help_study_load') : null;

        $delivery_target = $request->input('delivery_target');

        if ($delivery_target == 'International') {
            $domesticFee = null;
        } else {
            $domestic = $request->input('domestictuition_fee');
            if ($domestic == '') {
                $domesticFee = null;
            } else {
                $domesticFee = $request->input('domestictuition_fee');
            }
        }
        $courses->domestic_fee = $domesticFee;
        $courses->delivery_target = $delivery_target;
        $courses->tuition_fee = $request->input('tuition_fee');
        $courses->maximum_weekly_study = $request->input('maximum_weekly_study');
        $courses->qualification_prerequisite_id = (! empty($request->input('qualification_prerequisite'))) ? $request->input('qualification_prerequisite') : 'None';
        $courses->faculty_id = $request->input('faculty');
        $courses->department_id = $request->input('department');
        $courses->major = $request->input('major');

        $courses->course_recognition_id = $request->input('course_recognition');
        $courses->level_of_education_id = $request->input('level_of_education');
        $courses->field_of_education_id = $request->input('field_of_education');
        $courses->ANZSCO_code = $request->input('ANZSCO_code');
        $courses->total_nominal_hours = $request->input('total_nominal_hours');

        $campus_lists = $request->input('campusList');
        if (! empty($campus_lists)) {
            $campus_list = implode(',', $campus_lists);
            $courses->campus_list = $campus_list;
        } else {
            $campus_list = '';
        }
        $courses->results_calculation_methods = $request->input('results_calculation_methods');
        $work_placement_radio = $request->input('work_placement_radio');
        $courses->work_placement = $work_placement_radio;
        $courses->work_placement_hour = ($work_placement_radio == 1) ? $request->input('work_placement_hour') : null;
        $courses->placement_officer_id = ($work_placement_radio == 1) ? $request->input('placement_officer_id') : null;
        $courses->vocational_startweek = ($work_placement_radio == 1) ? $request->input('vocational_startweek') : null;
        $courses->vocational_duration = ($work_placement_radio == 1) ? $request->input('vocational_duration') : null;

        // $courses->couse_level = $request->input('course_level_radio');

        $courses->activated_now = (empty($request->input('Activate_radio'))) ? 0 : $request->input('Activate_radio');
        $courses->flexible_attendance = $request->input('Flexible_Attendance_Radio');
        $courses->course_delivery_type = $request->input('course_delivery_type');
        $courses->effective_start = (! empty($request->input('effective_start'))) ? date('Y-m-d', strtotime($request->input('effective_start'))) : null;
        $courses->effective_end = (! empty($request->input('effective_end'))) ? date('Y-m-d', strtotime($request->input('effective_end'))) : null;
        $courses->is_tcsi = $request->input('is_tcsi');
        $courses->course_completion_type = $request->input('course_completion_type');
        $courses->course_credit_point = $request->input('course_credit_point');
        $courses->qtac_course_id = $request->input('qtac_course_id');
        $courses->course_description = $request->input('course_description');

        $courses->save();

        DB::table('rto_course_campus')->where(['course_id' => $id, 'college_id' => $college_id])->delete();

        for ($i = 0; $i < count($campus_lists); $i++) {
            $coursesCampus = new CollegeCourse;
            $coursesCampus->college_id = $college_id;
            $coursesCampus->course_id = $id;
            $coursesCampus->campus_id = $campus_lists[$i];
            $coursesCampus->save();
        }
    }

    public function getCourseTypeList($college_id)
    {
        $arrCourseTypes = CourseType::select('id', 'title')
            ->where('status', 1)
            ->whereIn('college_id', [0, $college_id])
            ->get();

        return $arrCourseTypes;
    }

    public function getCourseTypeListV2($college_id)
    {
        $courseSelect = [];
        $arrCourseTypes = CourseType::select('id', 'title')
            ->where('status', 1)
            ->whereIn('college_id', [0, $college_id])
            ->pluck('title', 'id')
            ->toArray();
        $courseSelect[''] = '-- Select Course --';
        $returnData = $courseSelect + $arrCourseTypes;

        return $returnData;
    }

    public function getCourseListV2($college_id)
    {
        $courseSelect = [];
        $arrCourseTypes = Courses::where('status', 1)
            ->whereIn('college_id', [0, $college_id])
            ->get(['course_name', 'course_code', 'id'])
            ->toArray();

        $courseSelect[''] = '-- Select Course --';
        for ($i = 0; $i < count($arrCourseTypes); $i++) {
            $courseSelect[$arrCourseTypes[$i]['id']] = $arrCourseTypes[$i]['course_code'].':'.$arrCourseTypes[$i]['course_name'];
        }

        return $courseSelect;
    }

    public function getCourseListV3($college_id)
    {
        $courseSelect = [];
        $arrCourseTypes = Courses::where('status', 1)
            ->Where('college_id', $college_id)
            ->get(['course_name', 'course_code', 'id'])
            ->toArray();

        for ($i = 0; $i < count($arrCourseTypes); $i++) {
            $courseSelect[$arrCourseTypes[$i]['id']] = $arrCourseTypes[$i]['course_code'].':'.$arrCourseTypes[$i]['course_name'];
        }
        if (count($arrCourseTypes) == 0) {
            $courseSelect[''] = 'No Data Found';
        }

        return $courseSelect;
    }

    public function getCourseTypeListing($college_id)
    {

        $arrCourseTypes = CourseType::select('id', 'title')
            ->where('status', 1)
            ->whereIn('college_id', [0, $college_id])
            ->get()->toArray();

        return $arrCourseTypes;
    }

    public function getElicosVETCourseList($arrCourseTypes, $status, $collegeId)
    {

        $getElicosCourses = [];
        $getVETCourses = [];
        $getShortCourses = [];
        if ($status == 3) {
            foreach ($arrCourseTypes as $key => $arrCourseType) {
                $courseTypeId = $arrCourseType['id'];
                if ($courseTypeId == 1) {

                    $getElicosCourses = DB::table('rto_courses')
                        ->where('course_type_id', $courseTypeId)
                        ->where('college_id', $collegeId)
                        ->get();
                }
                if ($courseTypeId == 2) {
                    $getVETCourses = DB::table('rto_courses')
                        ->where('course_type_id', $courseTypeId)
                        ->where('college_id', $collegeId)
                        ->get();
                }
                if ($courseTypeId == 16) {
                    $getShortCourses = DB::table('rto_courses')
                        ->where('course_type_id', $courseTypeId)
                        ->where('college_id', $collegeId)
                        ->get();
                }
            }

            return $arrElicosVETCourses = ['arrElicos' => $getElicosCourses, 'arrVET' => $getVETCourses, 'arrShort' => $getShortCourses];
        } else {

            foreach ($arrCourseTypes as $key => $arrCourseType) {
                $courseTypeId = $arrCourseType['id'];
                if ($courseTypeId == 1) {
                    $getElicosCourses = Courses::where('course_type_id', $courseTypeId)
                        ->Where('activated_now', $status)
                        ->where('college_id', $collegeId)
                        ->get();
                }
                if ($courseTypeId == 2) {
                    $getVETCourses = Courses::where('course_type_id', $courseTypeId)
                        ->Where('activated_now', $status)
                        ->where('college_id', $collegeId)
                        ->get();
                }
                if ($courseTypeId == 16) {
                    $getShortCourses = Courses::where('course_type_id', $courseTypeId)
                        ->Where('activated_now', $status)
                        ->where('college_id', $collegeId)
                        ->get();
                }
            }

            return $arrElicosVETCourses = ['arrElicos' => $getElicosCourses, 'arrVET' => $getVETCourses, 'arrShort' => $getShortCourses];
        }
    }

    public function getAllCoursesList($status, $collegeId)
    {

        if ($status == 3) {

            $arrAllCourses = Courses::where([
                ['course_type_id', '<>', 1],
                ['course_type_id', '<>', 2],
            ])
                ->where('college_id', $collegeId)
                ->get();

            return $arrAllCourses;
        } else {

            $arrAllCourses = Courses::where([
                ['course_type_id', '<>', 1],
                ['course_type_id', '<>', 2],
            ])
                ->where('college_id', $collegeId)
                ->where('activated_now', $status)
                ->get();

            return $arrAllCourses;
        }
    }

    public function getANZSCOCodesList()
    {

        $arrANZSCOCode = AnzscoCodes::where('status', 1)
            ->pluck('name', 'avaitmiss_id')
            ->toArray();

        $ANZSCOCodesTitle['0'] = '-- Choose  ANZSCOCodes --';

        return $arrANZSCOCodes = $arrANZSCOCode;
    }

    public function getQualificationLevelList()
    {

        $arrQualificationLevel = QualificationLevel::where('status', '=', 1)->pluck('name', 'avaitmiss_id')->toArray();

        return $arrQualificationLevel;
    }

    public function getCoursesList($college_id)
    {

        $arrCourses = Courses::select('id', 'course_name', 'course_code')
            ->where('college_id', '=', $college_id)
            ->where('status', 1)
            ->get()->toArray();
        // print_r($arrCourses);exit;
        $arrQualifictionLevel = [];
        foreach ($arrCourses as $course) {
            $courseCode = $course['course_code'];
            $courseName = $course['course_name'];

            $arrQualifictionLevel[$courseCode] = $courseCode.' : '.$courseName;
        }

        return $arrQualifictionLevel;
    }

    public function getCoursesName($id)
    {

        $arrCoursesNane = Courses::select('id', 'course_name', 'course_code')
            ->where('id', '=', $id)->first();

        return $arrCoursesNane;
    }

    public function getCourseRecognition()
    {

        $arrCourseRecognition = CourseRecognitions::where('status', 1)
            ->pluck('name', 'avaitmiss_id')
            ->toArray();
        $arrCourseRecognitionTitle['0'] = '- - Choose Course Recognition - -';

        return $arrCourseRecognitions = $arrCourseRecognition;
    }

    public function getLevelEducation()
    {

        $arrLevelEducation = CourseLevelEducation::where('status', 1)
            ->pluck('name', 'avaitmiss_id')->toArray();

        $arrLevelEducationsTitle[''] = '- - Choose Level Of Education - -';

        return $arrLevelEducations = $arrLevelEducation;
    }

    public function getFieldOfEducation()
    {

        $arrFieldEducationTitle['0'] = '- - Choose Field Education - -';

        $arrFieldEducation = CourseFieldOfEducations::select('avaitmiss_id', 'name')
            ->where('status', 1)
            ->pluck('name', 'avaitmiss_id')->toArray();

        return $arrFieldEducations = $arrFieldEducation;
    }

    public function getDepartments($college_id)
    {

        $arrDepartments = Departments::join('rto_faculties', 'rto_departments.faculty_id', '=', 'rto_faculties.id')
            ->select('rto_departments.*', 'rto_faculties.name as fname')
            ->where('rto_departments.status', 1)
            ->Where('rto_departments.college_id', $college_id)
            ->get();

        return $arrDepartments;
    }

    public function getFacility($college_id)
    {

        $arrFinalFacilities['0'] = '- - Choose Faculty - -';

        $arrFacilitiess = Faculties::where('status', 1)
            ->Where('college_id', $college_id)
            ->pluck('name', 'id')
            ->toArray();

        return $arrFacility = $arrFinalFacilities + $arrFacilitiess;
    }

    public function getFacilities($college_id)
    {
        $arrFacilities = Faculties::select('id', 'name')
            ->where('status', 1)
            ->Where('college_id', $college_id)
            ->get();

        return $arrFacilities;
    }

    public function getStaffAndTeacher($college_id)
    {

        $default['0'] = '- - Choose Placement Officer - -';
        $staffAndTeacher = StaffPosition::from('rto_staff_position as rsp')
            ->join('rto_staff_and_teacher as rst', 'rst.position', '=', 'rsp.id')
            ->Where('college_id', $college_id)
            ->select(DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as name"), 'rst.id as id')
            ->get()
            ->toArray();
        $data = [];
        foreach ($staffAndTeacher as $key => $value) {
            $data[$staffAndTeacher[$key]['id']] = $staffAndTeacher[$key]['name'];
        }

        return $arrTeacher = $default + $data;
    }

    public function getCampusList($college_id)
    {
        $arrCampus = CollegeCampus::select('id', 'name')
            ->where('status', 1)
            ->Where('college_id', $college_id)
            ->get();

        return $arrCampus;
    }

    public function getCourseDelete($courseId)
    {
        return Courses::where('id', $courseId)->delete();
    }

    public function getCourseCampusDelete($courseId)
    {
        return CollegeCourse::where('course_id', $courseId)->delete();
    }

    public function getCourseFromType($courseType, $courseActive, $campusId, $college_id)
    {

        $data = Courses::select('id', 'course_name', 'course_code', 'course_type_id')
            ->Where('college_id', $college_id)
            ->where('course_type_id', $courseType)
            ->where('status', $courseActive)
            ->Where('activated_now', 1)
            // ->where('campus_list', 'LIKE', '%' . $campusId . '%')
            ->get();

        return $data;
    }

    public function getCourseFromTypeAgent($courseType, $courseActive, $college_id)
    {

        $data = Courses::select('id', 'course_code', 'course_name', 'course_type_id')
            ->where('course_type_id', $courseType)
            ->Where('activated_now', $courseActive)
            ->Where('status', 1)
            ->Where('college_id', $college_id)
            ->get();

        return $data;
    }

    public function getCourseFee($courseId, $college_id)
    {

        $arrCourses = Courses::select('id', 'course_name', 'tuition_fee', 'course_duration', 'couse_duration_type', 'course_code')
            ->where('id', $courseId)
            ->Where('college_id', $college_id)
            ->get();

        return $arrCourses;
    }

    public function getEnrollmentFee($collegeId, $courseStatus)
    {
        $arrCourseList = [];
        // $arrCourses = Courses::where('college_id','=',$collegeId)->where([['course_type_id', '=', $courseStatus]])->pluck('course_name', 'id');
        $arrCourses = Courses::where('college_id', '=', $collegeId)
            ->where('course_type_id', '=', $courseStatus)
            ->select('course_name', 'id', 'course_code')
            ->get()->toArray();
        foreach ($arrCourses as $row) {
            $arrCourseList[$row['id']] = $row['course_code'].' : '.$row['course_name'];
        }

        return $arrCourseList;
    }

    public function getCourseslistCollegeIdWise($courseId)
    {
        $data = Courses::where('id', $courseId)
            ->where('college_id', '=', Auth::user()->college_id)
            ->get();

        return $data;
    }

    public function getCoursesByCollege()
    {
        $courseData = [];
        $data = Courses::where('college_id', '=', Auth::user()->college_id)
            ->get(['course_name', 'course_code', 'id']);

        $courseRecord[''] = '- - Select Course - -';

        for ($i = 0; $i < count($data); $i++) {
            $courseData[$data[$i]['id']] = $data[$i]['course_code'].' : '.$data[$i]['course_name'];
        }

        $returnCourseRecord = $courseRecord + $courseData;

        return $returnCourseRecord;
    }

    public function _getCourses($collegeId, $courseTypeId, $courseStatus)
    {
        return Courses::where('college_id', '=', $collegeId)
            ->where('course_type_id', '=', $courseTypeId)
            ->where('activated_now', '=', $courseStatus)
            ->get(['course_name', 'id', 'course_code']);
    }

    public function getFullCoursesList($college_id)
    {
        $arrCourses = Courses::select('id', 'course_name', 'course_code')
            ->where('status', 1)
            ->get()->toArray();

        return $arrCourses;
    }

    public function getFullCoursesIntervention($collegeId)
    {
        $courseValue = [];
        $resultArr = Courses::where('status', 1)
            ->where('college_id', $collegeId)
            ->get(['id', 'course_name', 'course_code']);

        $courseValue[''] = '- - Select Course - -';
        $nullResult[''] = 'No Course Found';

        foreach ($resultArr as $row) {
            $courseValue[$row->id] = $row->course_code.' : '.$row->course_name;
        }
        $result = ($resultArr->count() > 0) ? $courseValue : $nullResult;

        return $result;
    }

    public function getSortCoursesArr($collegeId, $courseTypeID = '', $campusId = '')
    {

        $normalizedCourseType = strtolower(str_replace(' ', '', CourseType::COURSE_TYPE_SHORT));
        $courseTypeID = CourseType::whereRaw("LOWER(REPLACE(title, ' ', '')) = ?", [$normalizedCourseType])->value('id');
        $query = Courses::join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            ->select('rto_courses.id', 'rto_courses.course_name', 'rto_courses.course_code')
            ->where('rto_courses.status', '=', 1)
            ->where('rto_courses.activated_now', '=', 1)
            ->where('rto_courses.college_id', '=', $collegeId);

        if (! empty($campusId)) {
            $query->where('rto_course_campus.campus_id', '=', $campusId);
        }
        if (! empty($courseTypeID)) {
            $query->where('rto_courses.course_type_id', '=', $courseTypeID);
        }
        $arrCourses = $query->get()->toArray();

        $result[''] = '- - Select Course - -';
        foreach ($arrCourses as $key => $value) {
            $result[$arrCourses[$key]['id']] = $arrCourses[$key]['course_code'].' : '.$arrCourses[$key]['course_name'];
        }

        // echo "<pre/>";print_r($result);exit;
        return $result;
    }

    public function getFullCoursesArr($collegeId, $courseTypeID = '', $campusId = '')
    {
        $query = Courses::join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            ->select('rto_courses.id', 'rto_courses.course_name', 'rto_courses.course_code')
            ->where('rto_courses.status', '=', 1)
            ->where('rto_courses.activated_now', '=', 1)
            ->where('rto_courses.college_id', '=', $collegeId);

        if (! empty($campusId)) {
            $query->where('rto_course_campus.campus_id', '=', $campusId);
        }
        if (! empty($courseTypeID)) {
            $query->where('rto_courses.course_type_id', '=', $courseTypeID);
        }
        $arrCourses = $query->get()->toArray();

        $result[''] = '- - Select Course - -';
        foreach ($arrCourses as $key => $value) {
            $result[$arrCourses[$key]['id']] = $arrCourses[$key]['course_code'].' : '.$arrCourses[$key]['course_name'];
        }

        // echo "<pre/>";print_r($result);exit;
        return $result;
    }

    public function getCourseModuleWise($studentId, $moduleDelivery, $collegeId)
    {
        $res = Courses::select('course_code', 'course_name', 'id')
            ->where('college_id', '=', $collegeId)
            ->where('rto_courses.status', '=', 1)
            ->where('rto_courses.activated_now', '=', 1);
        if ($moduleDelivery == 'yes') {
            $res->where('module_delivery', '=', 1);
        }

        return $result = $res->get()
            ->toarray();
    }

    public function getCourseDetail($college_id, $course_id)
    {
        $arrCourse = Courses::select(
            'rto_courses.id',
            'rto_courses.course_name',
            'rto_courses.course_code',
            'rto_course_subject.course_id',
            'rto_subject.id as subject_id',
            'rto_subject.subject_name',
            'rto_subject.subject_code',
            DB::raw('IFNULL(group_concat(distinct rto_assessment_tasks.task_name separator "<br>"),"<span style=\'color:red;\'>No assessment info found</span>") as tasks'),
            DB::raw('group_concat(distinct concat(rto_subject_unit.vet_unit_code," : ",rto_subject_unit.unit_name) separator "<br>") as units')
        )
            ->leftjoin('rto_course_subject', 'rto_course_subject.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_subject.id')
            ->leftjoin('rto_assessment_tasks_units', 'rto_assessment_tasks_units.unit_id', '=', 'rto_subject_unit.id')
            ->leftjoin('rto_assessment_tasks', 'rto_assessment_tasks.id', '=', 'rto_assessment_tasks_units.assessment_task_id')
            ->where('rto_courses.college_id', $college_id)
            ->where('rto_courses.id', $course_id)
            ->where('rto_courses.status', 1)
            ->orderby('rto_subject.subject_name')
            ->groupby('rto_subject_unit.id')
            ->get()->toArray();

        return $arrCourse;
    }

    public function getCourseDetailForExport($college_id, $course_id)
    {
        $arrCourse = Courses::select('rto_courses.id', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_course_subject.course_id', 'rto_subject.id as subject_id', 'rto_subject.subject_name', 'rto_subject.subject_code', DB::raw('IFNULL(group_concat(distinct rto_assessment_tasks.task_name separator " ### "),"") as tasks'), DB::raw('group_concat(distinct concat(rto_subject_unit.vet_unit_code," : ",rto_subject_unit.unit_name) separator " ### ") as units'))
            ->leftjoin('rto_course_subject', 'rto_course_subject.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_subject.id')
            ->leftjoin('rto_assessment_tasks_units', 'rto_assessment_tasks_units.unit_id', '=', 'rto_subject_unit.id')
            ->leftjoin('rto_assessment_tasks', 'rto_assessment_tasks.id', '=', 'rto_assessment_tasks_units.assessment_task_id')
            ->where('rto_courses.college_id', $college_id)
            ->where('rto_courses.id', $course_id)
            ->where('rto_courses.status', 1)
            ->orderby('rto_subject.subject_name')
            ->groupby('rto_subject_unit.id')
            ->get()->toArray();

        return $arrCourse;
    }
    // get course list based on course type from course-calendar

    public function _getCourseByCourseType($courseTypeId, $collegeId, $courseStatus)
    {

        return Courses::where('course_type_id', '=', $courseTypeId)
            ->where('college_id', '=', $collegeId)
            ->where('activated_now', '=', $courseStatus)
            ->get(['id as course_id', 'course_code', 'course_name']);
    }

    public function _getCourseByCourseTypev2($courseTypeId, $collegeId, $courseStatus)
    {

        $arrCourse = Courses::where('course_type_id', '=', $courseTypeId)
            ->where('college_id', '=', $collegeId)
            ->where('activated_now', '=', $courseStatus)
            ->get(['id', 'course_code', 'course_name'])->toArray();
        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        return $result;
    }

    public function getCourseDataArray()
    {

        $arrCourse = Courses::select('id', 'course_code', 'course_name')->where('college_id', Auth::user()->college_id)->get()->toArray();
        $defaultCourseRecord[''] = '- - Select Course - -';
        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        return $returnCourseRecord = $defaultCourseRecord + $result;
    }

    public function getCourseDataArrayV2($collegeId)
    {

        $arrCourse = Courses::select('id', 'course_code', 'course_name')->where('college_id', $collegeId)->where('status', '=', 1)->get()->toArray();
        $defaultCourseRecord[''] = '- - Select Course - -';
        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        return $returnCourseRecord = $defaultCourseRecord + $result;
    }

    public function getCourseDataV2($statusValue, $collegeId)
    {

        $arrCourse = Courses::select('id', 'course_code', 'course_name')->where('college_id', $collegeId)->where('activated_now', '=', $statusValue)->get()->toArray();

        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        return [$result];
    }

    public function getCourseSingleStudentArr($college_id, $studentId)
    {

        $arrCourses = Courses::join('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_courses.id')
            ->select('rto_courses.id', 'rto_courses.course_name', 'rto_courses.course_code')
            ->where('rto_courses.college_id', '=', $college_id)
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.offer_status', '=', 'Enrolled')
            ->get()
            ->toArray();
        $result = [];
        $result[''] = '- - Select Course - -';
        foreach ($arrCourses as $key => $value) {
            $result[$arrCourses[$key]['id']] = $arrCourses[$key]['course_code'].' : '.$arrCourses[$key]['course_name'];
        }

        return $result;
    }

    public function getCourseCode($courseID)
    {
        return Courses::where('id', '=', $courseID)->get(['course_code', 'course_name']);
    }

    public function getCourseTypeId($courseID)
    {
        return Courses::where('id', '=', $courseID)->get(['course_type_id'])->toarray();
    }

    public function getFullCoursesV2($campusId, $courseTypeId, $collegeId)
    {

        $arrCourse = Courses::join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            ->select('rto_courses.id', 'rto_courses.course_code', 'rto_courses.course_name')
            ->where('rto_courses.college_id', $collegeId)
            ->where('rto_courses.course_type_id', $courseTypeId)
            ->where('rto_course_campus.campus_id', $campusId)
            ->get()->toArray();

        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        if (empty($result)) {
            $result[''] = 'No Course Found';
        }

        return $result;
    }

    public function getFlexibleCourses($campusId, $courseTypeId, $collegeId)
    {
        $arrCourse = Courses::join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            ->select('rto_courses.id', 'rto_courses.course_code', 'rto_courses.course_name')
            ->where('rto_courses.college_id', $collegeId)
            ->where('rto_courses.course_type_id', $courseTypeId)
            ->where('rto_course_campus.campus_id', $campusId)
            ->where('rto_courses.flexible_attendance', '1')
            ->get()->toArray();

        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        if (empty($result)) {
            $result[''] = 'No Course Found';
        }

        return $result;
    }

    public function getFullCourseList($collegeId, $firstCourseTypeKey)
    {

        $result = $this->_arrConvertCourseCodeWithName($collegeId, $firstCourseTypeKey);

        if (empty($result)) {
            $result[''] = 'No Course Found';
        } else {
            $result['0'] = 'All';
        }
        ksort($result);

        return $result;
    }

    public function getCourseList($collegeId, $firstCourseTypeKey)
    {
        $result = $this->_arrConvertCourseCodeWithName($collegeId, $firstCourseTypeKey);
        if (empty($result)) {
            $result[''] = 'No Course Found';
        }

        return $result;
    }

    public function _arrConvertCourseCodeWithName($collegeId, $firstCourseTypeKey)
    {

        if ($firstCourseTypeKey == 0) {
            $arrCourse = Courses::where('college_id', '=', $collegeId)->where('activated_now', '=', '1')->get(['id', 'course_code', 'course_name']);
        } else {
            $arrCourse = Courses::where('college_id', '=', $collegeId)
                ->where('course_type_id', '=', $firstCourseTypeKey)
                ->where('activated_now', '=', '1')
                ->get(['id', 'course_code', 'course_name']);
        }

        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        return $result;
    }

    public function getResultCalculationMethod($courseId)
    {

        return $arrCourse = Courses::select('results_calculation_methods', 'fee_help')
            ->where('college_id', '=', Auth::user()->college_id)
            ->where('id', '=', $courseId)
            ->get()
            ->toArray();
    }

    public function getCourseDataByFeeType($college_id)
    {

        $result = [];
        $arrCourseList = Courses::where('college_id', $college_id)
            ->where('fee_help', '1')
            ->get(['id', 'course_name', 'course_code'])
            ->toArray();

        if (count($arrCourseList) > 0) {
            foreach ($arrCourseList as $key => $value) {
                $result[$arrCourseList[$key]['id']] = $arrCourseList[$key]['course_code'].' : '.$arrCourseList[$key]['course_name'];
            }
        } else {
            $result[] = 'No Course Found';
        }

        return $result;
    }

    public function getCourseArrWithStatusForStudent($college_id, $studentId)
    {

        $arrCourses = Courses::join('rto_student_courses as rsc', 'rsc.course_id', '=', 'rto_courses.id')
            ->where('rto_courses.college_id', '=', $college_id)
            ->where('rsc.student_id', '=', $studentId)
            ->where('rsc.offer_status', '=', 'Enrolled')
            ->get([
                'rto_courses.id',
                'rto_courses.course_name',
                'rto_courses.course_code',
                'rsc.status',
            ])
            ->toArray();
        // echo '<pre>';print_r($arrCourses);exit;

        if (count($arrCourses) > 0) {
            foreach ($arrCourses as $key => $value) {
                $result[$arrCourses[$key]['id']] = $arrCourses[$key]['course_code'].' : '.$arrCourses[$key]['course_name'].' - '.$arrCourses[$key]['status'];
            }
        } else {
            $result[''] = 'No Course Found';
        }

        return $result;
    }

    /* THIS FUNCTION IS USED FOR GENERATE XLS FILE ONLY (NAT00030) */

    public function getCourseListByCollageId($college_id, $arrFilter)
    {

        $sql = Courses::leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
            })
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_student_subject_enrolment.subject_id')
            ->where('rto_courses.status', 1)
            ->Where('rto_courses.college_id', $college_id)
            ->groupBy('rto_courses.id');

        $sql = $this->applyGlobalFilter($sql, $arrFilter, 'nat30');

        $sql = $this->applySmartAndSkillFilter($sql, $arrFilter);

        $arrCourseTypes = $sql->get([
            'rto_courses.*',
            'rto_venue.state',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_courses.is_claim',
            'rto_student_subject_enrolment.activity_finish_date',
            'rto_subject_unit.vet_flag',
        ]);

        return $arrCourseTypes;
    }

    public function getCourseIDFromCode($courseCode, $collegeId)
    {
        $resultArr = Courses::where('college_id', '=', $collegeId)
            ->where('course_code', $courseCode)
            ->get(['id']);

        return $resultArr[0]->id;
    }

    public function getCourseListByCollageIdForNSW($college_id, $arrFilter)
    {

        $sql = Courses::leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_anzsco_codes', 'rto_anzsco_codes.id', '=', 'rto_courses.ANZSCO_code')
            ->leftjoin('rto_course_recognitions', 'rto_course_recognitions.id', '=', 'rto_courses.course_recognition_id')
            ->leftjoin('rto_field_of_educations', 'rto_field_of_educations.id', '=', 'rto_courses.field_of_education_id')
            ->leftjoin('rto_level_of_educations', 'rto_level_of_educations.id', '=', 'rto_courses.level_of_education_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_student_subject_enrolment.subject_id')
            ->where('rto_courses.status', 1)
            ->Where('rto_courses.college_id', $college_id)
            ->groupBy('rto_courses.id');
        if (! empty($arrFilter['claim_stage'])) {
            $sql->leftjoin('rto_student_claim_tracking', 'rto_student_claim_tracking.student_id', '=', 'rto_student_courses.student_id');
            $sql->where('rto_student_claim_tracking.lodgement_type', $arrFilter['claim_stage']);
        }
        $sql->where('rto_venue.state', '=', 'NSW');

        $arrCourseTypes = $sql->get([
            'rto_courses.*',
            'rto_venue.state',
            'rto_anzsco_codes.avaitmiss_id as ANZSCO_code',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_courses.is_claim',
            'rto_student_subject_enrolment.activity_finish_date',
            'rto_subject_unit.vet_flag',
            'rto_course_recognitions.avaitmiss_id as course_recognition_id',
            'rto_level_of_educations.avaitmiss_id as level_of_education',
            'rto_field_of_educations.avaitmiss_id as field_of_education',
        ]);

        return $arrCourseTypes;
    }

    public function getFullCoursesV2Arr($campusId, $courseTypeId, $collegeId, $activeCourse)
    {

        $nullResult[''] = 'No Course Found';
        $arrCourse = Courses::from('rto_courses as rc')
            ->join('rto_course_campus as rcc', 'rcc.course_id', '=', 'rc.id')
            ->select('rc.id', 'rc.course_code', 'rc.course_name')
            ->where('rc.college_id', $collegeId)
            ->where('rc.course_type_id', $courseTypeId)
            ->where('rcc.campus_id', $campusId)
            ->where('rc.activated_now', $activeCourse)
            ->get()->toArray();

        $result = [];
        foreach ($arrCourse as $key => $value) {
            $result[$arrCourse[$key]['id']] = $arrCourse[$key]['course_code'].' : '.$arrCourse[$key]['course_name'];
        }

        $res = (! empty($result)) ? $result : $nullResult;

        return $res;
    }
}
