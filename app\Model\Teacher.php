<?php

namespace App\Model;

use App\Model\Traits\TeacherFilterTrait;
use Auth;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class Teacher extends Model
{
    use TeacherFilterTrait;

    protected $table = 'rto_staff_and_teacher';

    protected $fillable = [
        'college_id',
        'user_id',
        'name_title',
        'first_name',
        'last_name',
        'country',
        'address',
        'city_town',
        'state',
        'postcode',
        'phone',
        'mobile',
        'email',
        'personal_email',
        'birth_date',
        'gender',
        'atsi_code',
        'joining_date',
        'highest_qualification_code',
        'highest_qualification_place_code',
        'work_contract_code',
        'staff_work_level_code',
        'organisational_unit_code',
        'work_sector_code',
        'function_code',
        'signatory_text',
        'staff_number',
        'position',
        'staff_type',
        'is_active',
        'updated_by',
        'created_by',
    ];

    public function saveTeacherDetails($lastUserId, $request)
    {

        $objTeacher = new Teacher;
        $objTeacher->college_id = Auth::user()->college_id;
        $objTeacher->user_id = $lastUserId;
        $objTeacher->name_title = ($request->input('name_title') != '') ? $request->input('name_title') : null;
        $objTeacher->first_name = ($request->input('first_name') != '') ? $request->input('first_name') : null;
        $objTeacher->last_name = ($request->input('last_name') != '') ? $request->input('last_name') : null;
        $objTeacher->country = ($request->input('country') != '') ? $request->input('country') : null;
        $objTeacher->address = ($request->input('address') != '') ? $request->input('address') : null;
        $objTeacher->city_town = ($request->input('city_town') != '') ? $request->input('city_town') : null;
        $objTeacher->state = ($request->input('state') != '') ? $request->input('state') : null;
        $objTeacher->postcode = ($request->input('postcode') != '') ? $request->input('postcode') : null;
        $objTeacher->phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $objTeacher->mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $objTeacher->email = ($request->input('email') != '') ? $request->input('email') : null;
        $objTeacher->personal_email = ($request->input('personal_email') != '') ? $request->input('personal_email') : null;

        $objTeacher->birth_date = ($request->input('birth_date') != '') ? $request->input('birth_date') : null;
        $objTeacher->joining_date = ($request->input('joining_date') != '') ? date('Y-m-d', strtotime($request->input('joining_date'))) : null;
        $objTeacher->gender = ($request->input('gender') != '') ? $request->input('gender') : null;
        $objTeacher->highest_qualification_code = ($request->input('highest_qualification_code') != '') ? $request->input('highest_qualification_code') : null;
        $objTeacher->highest_qualification_place_code = ($request->input('highest_qualification_place_code') != '') ? $request->input('highest_qualification_place_code') : null;
        $objTeacher->work_contract_code = ($request->input('work_contract_code') != '') ? $request->input('work_contract_code') : null;
        $objTeacher->staff_work_level_code = ($request->input('staff_work_level_code') != '') ? $request->input('staff_work_level_code') : null;
        $objTeacher->organisational_unit_code = ($request->input('organisational_unit_code') != '') ? $request->input('organisational_unit_code') : null;
        $objTeacher->work_sector_code = ($request->input('work_sector_code') != '') ? $request->input('work_sector_code') : null;
        $objTeacher->function_code = ($request->input('function_code') != '') ? $request->input('function_code') : null;
        $objTeacher->atsi_code = ($request->input('atsi_code') != '') ? $request->input('atsi_code') : null;

        $objTeacher->signatory_text = ($request->input('signatory_text') != '') ? $request->input('signatory_text') : null;
        $objTeacher->staff_number = ($request->input('staff_number') != '') ? $request->input('staff_number') : null;
        $objTeacher->position = ($request->input('position') != '') ? $request->input('position') : null;
        $objTeacher->staff_type = 'Staff-Teacher';
        $objTeacher->is_active = ($request->input('status') != '') ? $request->input('status') : null;

        $objTeacher->created_by = Auth::user()->id;
        $objTeacher->updated_by = Auth::user()->id;
        $objTeacher->save();

        return $objTeacher->id;
    }

    public function editTeacherDetails($teacherId, $request)
    {

        $objTeacher = Teacher::find($teacherId);
        $objTeacher->name_title = ($request->input('name_title') != '') ? $request->input('name_title') : null;
        $objTeacher->first_name = ($request->input('first_name') != '') ? $request->input('first_name') : null;
        $objTeacher->last_name = ($request->input('last_name') != '') ? $request->input('last_name') : null;
        $objTeacher->country = ($request->input('country') != '') ? $request->input('country') : null;
        $objTeacher->address = ($request->input('address') != '') ? $request->input('address') : null;
        $objTeacher->city_town = ($request->input('city_town') != '') ? $request->input('city_town') : null;
        $objTeacher->state = ($request->input('state') != '') ? $request->input('state') : null;
        $objTeacher->postcode = ($request->input('postcode') != '') ? $request->input('postcode') : null;
        $objTeacher->phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $objTeacher->mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $objTeacher->email = ($request->input('email') != '') ? $request->input('email') : null;
        $objTeacher->personal_email = ($request->input('personal_email') != '') ? $request->input('personal_email') : null;
        $objTeacher->signatory_text = ($request->input('signatory_text') != '') ? $request->input('signatory_text') : null;
        $objTeacher->staff_number = ($request->input('staff_number') != '') ? $request->input('staff_number') : null;
        $objTeacher->position = ($request->input('position') != '') ? $request->input('position') : null;
        $objTeacher->is_active = ($request->input('status') != '') ? $request->input('status') : null;

        $objTeacher->birth_date = ($request->input('birth_date') != '') ? date('Y-m-d', strtotime($request->input('birth_date'))) : null;
        $objTeacher->joining_date = ($request->input('joining_date') != '') ? date('Y-m-d', strtotime($request->input('joining_date'))) : null;
        $objTeacher->gender = ($request->input('gender') != '') ? $request->input('gender') : null;
        $objTeacher->highest_qualification_code = ($request->input('highest_qualification_code') != '') ? $request->input('highest_qualification_code') : null;
        $objTeacher->highest_qualification_place_code = ($request->input('highest_qualification_place_code') != '') ? $request->input('highest_qualification_place_code') : null;
        $objTeacher->work_contract_code = ($request->input('work_contract_code') != '') ? $request->input('work_contract_code') : null;
        $objTeacher->staff_work_level_code = ($request->input('staff_work_level_code') != '') ? $request->input('staff_work_level_code') : null;
        $objTeacher->organisational_unit_code = ($request->input('organisational_unit_code') != '') ? $request->input('organisational_unit_code') : null;
        $objTeacher->work_sector_code = ($request->input('work_sector_code') != '') ? $request->input('work_sector_code') : null;
        $objTeacher->function_code = ($request->input('function_code') != '') ? $request->input('function_code') : null;
        $objTeacher->atsi_code = ($request->input('atsi_code') != '') ? $request->input('atsi_code') : null;

        $objTeacher->updated_by = Auth::user()->id;
        $objTeacher->save();

        return $objTeacher->user_id;
    }

    public function getTeacherList($perPage)
    {
        return Teacher::join('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            ->where('rto_staff_and_teacher.college_id', '=', Auth::user()->college_id)
            ->where('rto_staff_and_teacher.position', '=', 7)
            ->orderBy('rto_staff_and_teacher.id', 'DESC')
            ->select('rto_users.username', 'rto_staff_and_teacher.*')
            ->paginate($perPage);
    }

    public function getTeacherData($teacherId)
    {
        return Teacher::where('college_id', '=', Auth::user()->college_id)->where('id', '=', $teacherId)->get();
    }

    public function getTeacherDataArray()
    {

        $arrTeacher = Teacher::from('rto_staff_and_teacher as rst')
            ->join('rto_users', 'rto_users.id', '=', 'rst.user_id')
            ->where('rst.college_id', '=', Auth::user()->college_id)
            ->where('rst.position', '=', 7)
            ->where('rst.is_active', '=', 1)
            ->select(DB::raw('concat(rst.name_title," ",rst.first_name," ",rst.last_name) as first_name'), 'rst.id')
            ->pluck('first_name', 'rst.id')
            // ->pluck(DB::raw('concat(rst.name_title," ",rst.first_name," ",rst.last_name) as first_name'), 'rst.id')
            // ->pluck('rst.first_name', 'rst.id')
            ->toArray();
        $nullRecord[''] = 'No Teacher record found';

        return $returnTeacherRecord = (count($arrTeacher) > 0) ? $arrTeacher : $nullRecord;
    }

    public function deleteTeacher($teacherId)
    {
        $getUserId = Teacher::where('college_id', '=', Auth::user()->college_id)->where('id', '=', $teacherId)->get(['user_id']);
        $deleteTeacher = Teacher::where('id', '=', $teacherId)->delete();

        return $getUserId;
    }

    public function getTeacherProfile($teacherId)
    {
        return Teacher::leftjoin('rto_users as ru', 'ru.id', '=', 'rto_staff_and_teacher.user_id')
            ->where('rto_staff_and_teacher.college_id', '=', Auth::user()->college_id)
            ->where('rto_staff_and_teacher.id', '=', $teacherId)->get(['ru.username', 'rto_staff_and_teacher.*']);
    }

    public function _searchTeacherData($searchBy, $searchString, $searchStatus, $collegeId)
    {

        $objTeacherList = Teacher::join('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            ->where('rto_staff_and_teacher.college_id', '=', $collegeId)
            ->where('rto_staff_and_teacher.position', '=', 7)
            ->orderBy('rto_staff_and_teacher.id', 'DESC')
            ->select('rto_users.username', 'rto_staff_and_teacher.*');

        if ($searchBy == 'username' && $searchBy != '') {
            $objTeacherList->where('rto_users.username', 'like', '%'.$searchString.'%');
        }
        if ($searchBy == 'first_name' && $searchBy != '') {
            $objTeacherList->where('rto_staff_and_teacher.first_name', 'like', '%'.$searchString.'%');
        }
        if ($searchBy == 'last_name' && $searchBy != '') {
            $objTeacherList->where('rto_staff_and_teacher.last_name', 'like', '%'.$searchString.'%');
        }
        if ($searchBy == 'email' && $searchBy != '') {
            $objTeacherList->where('rto_staff_and_teacher.email', 'like', '%'.$searchString.'%');
        }
        if ($searchBy == 'is_active' && $searchBy != '') {
            if ($searchStatus == 2) {
                $searchStatus = 0;
            }
            $objTeacherList->where('rto_staff_and_teacher.is_active', '=', $searchStatus);
        }

        $resultData = $objTeacherList->get();

        return $resultData;
    }

    public function _getTeacherManageData($collegeId, $request)
    {
        $requestData = $_REQUEST;
        $columns = [
            0 => 'rto_staff_and_teacher.staff_number',
            1 => 'rto_staff_and_teacher.first_name',
            2 => 'rto_users.username',
            3 => 'rto_users.email',
            4 => 'rto_staff_and_teacher.is_active',
            5 => 'rto_staff_and_teacher.address',
            6 => 'rto_staff_and_teacher.city_town',
        ];

        $query = Teacher::join('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            ->where('rto_staff_and_teacher.college_id', '=', $collegeId)
            ->where('rto_staff_and_teacher.position', '=', 7);
        //                ->orderBy('rto_staff_and_teacher.id', 'DESC')

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('rto_staff_and_teacher.staff_number', 'rto_staff_and_teacher.first_name', 'rto_users.username', 'rto_users.email', 'rto_staff_and_teacher.last_name', 'rto_staff_and_teacher.is_active', 'rto_staff_and_teacher.address', 'rto_staff_and_teacher.city_town', 'rto_staff_and_teacher.id')
            ->get();

        $data = [];
        foreach ($resultArr as $row) {
            $actionHtml = '';
            $actionHtml .= '<li><a class="link-black text-sm"  data-original-title="Go to trainer profile page in new window"  id="'.$row['id'].'" data-id="'.$row['id'].'" data-toggle="tooltip"  data-load-remote="'.route('intervention-letter', ['id' => $row['id']]).'" href="#myModal"><i class="fa  fa-home "></i></a></li>';
            $actionHtml .= '<li><a href="'.route('teacher-profile', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="Go to trainer profile page" ><i class="fa fa-graduation-cap "></i></a></li>';
            $actionHtml .= '<li><span data-toggle="modal" class="delete" data-id="'.$row['id'].'" data-target="#deleteModal">
                            <a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove "></i> </a> </span></li>';

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                '.$actionHtml.'
                            </ul>
                        </div>';

            $status = ($row['is_active'] == 1) ? 'checked="checked"' : '';
            $nestedData = [];
            $nestedData[] = $row['staff_number'].$action;
            $nestedData[] = $row['first_name'].' '.Str::ascii($row['last_name']);
            $nestedData[] = $row['username'];
            $nestedData[] = $row['email'];
            //            $nestedData[] = '<div class=""> <input type="checkbox" class="icheckbox_flat-green" name="status"  id="status" ' . $status . ' disabled>
            //                             </div>';
            $nestedData[] = '<div class=""> <input type="checkbox" class="flat-red label-value-view applicable custom-checkbox-input single" name="status"  id="status" '.$status.' disabled>
                             <label style="" for="status" class="control-label custom-checkbox"> </label></div>';
            $nestedData[] = $row['address'];
            $nestedData[] = $row['city_town'];
            $data[] = $nestedData;
        }

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function getTeacherNameList($collegeId)
    {
        $arrTeacher = Teacher::where('college_id', '=', $collegeId)
            ->select('id', 'name_title', 'first_name', 'last_name')
            ->get()->toArray();
        $teacherList[''] = '- - Select Escalated To - -';
        foreach ($arrTeacher as $arr) {
            $teacherList[$arr['id']] = $arr['name_title'].' '.$arr['first_name'].' '.$arr['last_name'];
        }

        return $teacherList;
    }

    public function getTeacherName($teacherId)
    {
        $result = Teacher::where('college_id', '=', Auth::user()->college_id)
            ->where('id', '=', $teacherId)
            ->select('id', 'name_title', 'first_name', 'last_name')
            ->first();
        if (! $result) {
            return '';
        }

        return trim("{$result->name_title} {$result->first_name} {$result->last_name}");
    }

    public function getTeacherNStaffForLeave($type)
    {
        $arrTeacher = Teacher::where('college_id', '=', Auth::user()->college_id)
            ->where('is_active', '=', 1)
            ->where('staff_type', '=', $type)
            ->select('id', DB::raw('concat(name_title," ",first_name," ",last_name) as fullName'))
            ->pluck('fullName', 'id')
            ->toArray();

        $teacherRecord[''] = '- - Select User - -';

        return $teacherRecord + $arrTeacher;
    }

    public function editTeacherFromTeacherProfile($collegeId, $request, $userId)
    {
        $username = ($request->input('username') != '') ? $request->input('username') : null;
        $phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $email = ($request->input('email') != '') ? $request->input('email') : null;
        $name = ($request->input('name') != '') ? $request->input('name') : null;

        $arrName = explode(' ', $name);
        $first_name = $arrName[0];
        $family_name = ($arrName[1] != '') ? $arrName[1] : null;

        $objRtoUsers = Teacher::where('user_id', '=', $userId)->where('college_id', '=', $collegeId)->update([
            'first_name' => $first_name,
            'last_name' => $family_name,
            'phone' => $phone,
            'mobile' => $mobile,
            'email' => $email,
        ]);

        return true;
    }

    public function afterCreateProcess()
    {
        $request = request();
        $arrTeacherRoleType = Config::get('constants.arrTeacherRoleType');

        if (! $request->filled('username') || ! $request->filled('password')) {
            return;
        }

        try {
            $user = new Users;
            $existingUser = $user->where('username', $request->input('username'))
                ->orWhere('email', $request->input('email'))
                ->first();

            if ($existingUser) {
                throw new \Exception('User already exists');
            }

            $user->college_id = auth()->user()->college_id;
            $user->name = $request->input('first_name').' '.$request->input('last_name');
            $user->username = $request->input('username');
            $user->password = Hash::make($request->input('password'));
            $user->role_id = key($arrTeacherRoleType);
            $user->email = $request->input('email');
            $user->phone = $request->input('phone');
            $user->mobile = $request->input('mobile');
            $user->security_question = $request->input('security_question');
            $user->security_answer = $request->input('security_answer');
            $user->status = $request->input('status', 1);
            $userSaved = $user->save();

            if ($userSaved) {
                $this->user_id = $user->id;
                $modelSaved = $this->save();
            } else {
                throw new \Exception('Failed to save user');
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
