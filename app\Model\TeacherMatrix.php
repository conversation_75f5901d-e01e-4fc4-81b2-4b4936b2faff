<?php

namespace App\Model;

use App\Model\Traits\TeacherMatrixFilterTrait;
use Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class TeacherMatrix extends Model
{
    use TeacherMatrixFilterTrait;

    protected $table = 'rto_teacher_matrix';

    public function saveTeacherMatrix($request)
    {
        $totalSubjects = $request->input('subject_id');

        $errMsg = '';
        foreach ($totalSubjects as $key => $value) {
            $getMatrixData = TeacherMatrix::where('college_id', '=', Auth::user()->college_id)
                ->where('teacher_id', '=', $request->input('teacher_id'))
                ->where('course_id', '=', $request->input('course_id'))
                ->where('subject_id', '=', $value)
                ->select('id')
                ->get();

            $objSubject = new Subject;
            $ArrSubject = $objSubject->getSubjectlistCollegeIdWise($value);

            if ($getMatrixData->count() == 0) {

                $objTeacherMatrix = new TeacherMatrix;
                $objTeacherMatrix->college_id = Auth::user()->college_id;
                $objTeacherMatrix->teacher_id = ($request->input('teacher_id') != '') ? $request->input('teacher_id') : null;
                $objTeacherMatrix->course_id = ($request->input('course_id') != '') ? $request->input('course_id') : null;
                $objTeacherMatrix->course_status = ($request->input('course_status') != '') ? $request->input('course_status') : null;
                $objTeacherMatrix->subject_id = $value;
                $objTeacherMatrix->knowledge_level = ($request->input('knowledge_level') != '') ? $request->input('knowledge_level') : null;
                $objTeacherMatrix->created_by = Auth::user()->id;
                $objTeacherMatrix->updated_by = Auth::user()->id;
                $objTeacherMatrix->save();
            } else {
                $errMsg .= $ArrSubject[0]['subject_code'].' : '.$ArrSubject[0]['subject_name'].', ';
                //                $returnData['type'] = 'session_error';
                //                $returnData['message'] = 'Record already Exists for This Teacher.';
                //                return $returnData;
            }
        }

        return $errMsg;
    }

    public function getTeacherMatrixList($perPage)
    {
        return $data = TeacherMatrix::join('rto_staff_and_teacher as rst', 'rto_teacher_matrix.teacher_id', '=', 'rst.id')
            ->join('rto_courses', 'rto_teacher_matrix.course_id', '=', 'rto_courses.id')
            ->join('rto_subject', 'rto_teacher_matrix.subject_id', '=', 'rto_subject.id')
            ->where('rto_teacher_matrix.college_id', '=', Auth::user()->college_id)
            ->orderBy('rto_teacher_matrix.id', 'DESC')
            ->select('rst.first_name', 'rto_teacher_matrix.*', 'rto_courses.course_name', 'rto_subject.subject_code', 'rto_subject.subject_name')
            ->paginate($perPage);
    }

    public function getTeacherMatrixByTeacher($requestData, $teacherId, $perPage, $isDataTable = false)
    {
        if ($isDataTable) {

            $query = TeacherMatrix::join('rto_subject as rs', 'rs.id', '=', 'rto_teacher_matrix.subject_id')->where('rto_teacher_matrix.college_id', '=', Auth::user()->college_id)
                ->where('rto_teacher_matrix.teacher_id', '=', $teacherId);

            $columns = [
                // datatable column index  => database column name
                1 => 'rs.subject_code',
                2 => 'rs.subject_name',
                3 => 'rto_teacher_matrix.course_status',
            ];
            $totalData = 0;
            $totalFiltered = 0;
            if ($requestData['order'][0]['column'] != '' && $requestData['order'][0]['dir'] != '') {
                $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);
                $totalData = $temp->count();
                $totalFiltered = $temp->count();
            }
            $resultArr = $query->select('rs.subject_code', 'rs.subject_name', 'rto_teacher_matrix.*')
                ->skip($requestData['start'])
                ->take($requestData['length'])
                ->get();
            $data = [];
            foreach ($resultArr as $row) {

                $nestedData = [];
                $nestedData[] = $row['subject_code'];
                $nestedData[] = $row['subject_name'];
                if ($row['course_status'] == 1) {
                    $nestedData[] = 'Advanced';
                } else {
                    $nestedData[] = 'Intermediate';
                }
                $data[] = $nestedData;
            }

            return [
                'draw' => intval($requestData['draw']),
                'recordsTotal' => intval($totalData),
                'recordsFiltered' => intval($totalFiltered),
                'data' => $data,
            ];
        } else {
            return TeacherMatrix::join('rto_subject as rs', 'rs.id', '=', 'rto_teacher_matrix.subject_id')
                ->where('rto_teacher_matrix.college_id', '=', Auth::user()->college_id)
                ->where('rto_teacher_matrix.teacher_id', '=', $teacherId)
                ->select('rs.subject_code', 'rs.subject_name', 'rto_teacher_matrix.*')
                ->paginate($perPage);
        }
    }

    public function getTeacherMatrixListAjax($teacherId)
    {
        if ($teacherId != '') {
            return $data = TeacherMatrix::join('rto_staff_and_teacher', 'rto_teacher_matrix.teacher_id', '=', 'rto_staff_and_teacher.id')
                ->join('rto_courses', 'rto_teacher_matrix.course_id', '=', 'rto_courses.id')
                ->join('rto_subject', 'rto_teacher_matrix.subject_id', '=', 'rto_subject.id')
                ->where('rto_teacher_matrix.college_id', '=', Auth::user()->college_id)
                ->where('rto_teacher_matrix.teacher_id', '=', $teacherId)
                ->orderBy('rto_teacher_matrix.id', 'DESC')
                ->select('rto_staff_and_teacher.first_name', 'rto_teacher_matrix.*', 'rto_courses.course_name', 'rto_subject.subject_code', 'rto_subject.subject_name')
                ->get();
        } else {
            return $data = TeacherMatrix::join('rto_staff_and_teacher', 'rto_teacher_matrix.teacher_id', '=', 'rto_staff_and_teacher.id')
                ->join('rto_courses', 'rto_teacher_matrix.course_id', '=', 'rto_courses.id')
                ->join('rto_subject', 'rto_teacher_matrix.subject_id', '=', 'rto_subject.id')
                ->where('rto_teacher_matrix.college_id', '=', Auth::user()->college_id)
                ->where('rto_teacher_matrix.teacher_id', '=', $teacherId)
                ->orderBy('rto_teacher_matrix.id', 'DESC')
                ->select('rto_staff_and_teacher.first_name', 'rto_teacher_matrix.*', 'rto_courses.course_name', 'rto_subject.subject_code', 'rto_subject.subject_name')
                ->get();
        }
    }

    public function deleteTeacherMatrix($teacherMatrixId)
    {
        return TeacherMatrix::where('id', '=', $teacherMatrixId)->delete();
    }

    public function deleteTeacher($teacherId)
    {
        return TeacherMatrix::where('teacher_id', '=', $teacherId)->delete();
    }

    public function getTeacherMatrixData($teacherMatrixId)
    {

        return $query = TeacherMatrix::leftjoin('rto_subject', 'rto_teacher_matrix.subject_id', '=', 'rto_subject.id')
            ->where('rto_teacher_matrix.college_id', '=', Auth::user()->college_id)
            ->where('rto_teacher_matrix.id', '=', $teacherMatrixId)
            ->select('rto_teacher_matrix.*', 'rto_subject.subject_name as subject_name')
            ->get();
        //        return TeacherMatrix::where('college_id', '=', Auth::user()->college_id)->where('id', '=', $teacherMatrixId)->get();
    }

    public function editTeacherMatrix($teacherMatrixId, $request)
    {

        $objTeacherMatrix = TeacherMatrix::find($teacherMatrixId);
        $objTeacherMatrix->knowledge_level = ($request->input('knowledge_level') != '') ? $request->input('knowledge_level') : null;
        $objTeacherMatrix->updated_by = Auth::user()->id;
        $objTeacherMatrix->save();
    }

    public function getCourseListForCourseMaterial($collegeId, $teacherId)
    {
        $resultArr = TeacherMatrix::from('rto_teacher_matrix as rtm')
            ->join('rto_courses as rc', 'rc.id', 'rtm.course_id')
            ->where('rtm.college_id', '=', $collegeId)
            ->where('rtm.teacher_id', '=', $teacherId)
            ->groupBy('rtm.course_id')
            ->get(['rc.id', 'rc.course_code', 'rc.course_name']);
        $result = [];
        foreach ($resultArr as $row) {
            $result[] = $row->course_code;
        }

        return $result;
    }

    public function _getTeacherFromSubject($collegeId, $subjectId)
    {
        return $data = TeacherMatrix::join('rto_staff_and_teacher as rst', 'rto_teacher_matrix.teacher_id', '=', 'rst.id')
            ->join('rto_courses', 'rto_teacher_matrix.course_id', '=', 'rto_courses.id')
            ->join('rto_subject', 'rto_teacher_matrix.subject_id', '=', 'rto_subject.id')
            ->where('rto_teacher_matrix.college_id', '=', $collegeId)
            ->where('rto_teacher_matrix.subject_id', '=', $subjectId)
            ->where('rst.is_active', '=', 1)
            ->groupBy('rst.id')
            ->orderBy('rto_teacher_matrix.id', 'DESC')
            ->get([
                'rst.id as teacher_id',
                'rst.name_title',
                'rst.first_name',
                'rst.last_name',
                'rst.email',
                'rst.is_active',
            ]);
    }

    public function getTeacherMetrixData($collegeId, $request, $arrKnowledgeLevel)
    {
        $requestData = $_REQUEST;
        unset($arrKnowledgeLevel['']);

        $columns = [
            // datatable column index  => database column name
            0 => 'rto_staff_and_teacher.first_name',
            1 => 'rto_subject.subject_code',
            2 => 'rto_subject.subject_name',
            3 => 'rto_teacher_matrix.knowledge_level',
            4 => 'rto_courses.course_name',
            5 => 'rto_courses.course_code',
            6 => 'rto_courses.national_code',
            7 => 'rto_courses.cricos_code',
        ];

        $query = TeacherMatrix::join('rto_staff_and_teacher', 'rto_teacher_matrix.teacher_id', '=', 'rto_staff_and_teacher.id')
            ->join('rto_courses', 'rto_teacher_matrix.course_id', '=', 'rto_courses.id')
            ->join('rto_subject', 'rto_teacher_matrix.subject_id', '=', 'rto_subject.id')
            ->where('rto_staff_and_teacher.position', '=', 7)
            ->where('rto_teacher_matrix.college_id', '=', $collegeId);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData, $arrKnowledgeLevel) {
                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 3 && in_array(ucwords($searchVal), $arrKnowledgeLevel)) {
                        $searchVal = array_search(ucwords($searchVal), $arrKnowledgeLevel);
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('rto_staff_and_teacher.first_name', 'rto_teacher_matrix.knowledge_level', 'rto_teacher_matrix.id', 'rto_courses.course_name', 'rto_courses.national_code', 'rto_courses.course_code', 'rto_courses.cricos_code', 'rto_subject.subject_code', 'rto_subject.subject_name')
            ->get();
        //        echo "<pre>";
        //        print_r($resultArr);

        $data = [];
        foreach ($resultArr as $row) {
            $nestedData = [];
            $actionHtml = '';
            $actionHtml .= '<li><a class="link-black text-sm edit" data-toggle="tooltip" data-original-title="Edit" href="'.route('edit-teacher-matrix', ['id' => $row['id']]).'"><i class="fa fa-edit"></i></a></li>';
            $actionHtml .= '<li><span data-toggle="modal" class="delete" data-id="'.$row['id'].'" data-target="#deleteModal"><a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a></span></li>';
            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                              '.$actionHtml.'
                            </ul>
                      </div>';
            $nestedData[] = $row['first_name'].$action;
            $nestedData[] = $row['subject_code'];
            $nestedData[] = Str::ascii($row['subject_name']);
            $nestedData[] = $arrKnowledgeLevel[$row['knowledge_level']];
            $nestedData[] = $row['course_name'];
            $nestedData[] = $row['course_code'];
            $nestedData[] = $row['national_code'];
            $nestedData[] = $row['cricos_code'];
            $data[] = $nestedData;
        }
        //        echo "<pre>";print_r($data);exit;

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }
}
