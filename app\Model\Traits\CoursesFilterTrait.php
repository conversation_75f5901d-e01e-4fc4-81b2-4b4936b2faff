<?php

namespace App\Model\Traits;

trait CoursesFilterTrait
{
    public function scopeFilterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('course_name', 'like', $searchTerm)
                ->orWhere('course_duration', 'like', $searchTerm)
                ->orWhere('delivery_target', 'like', $searchTerm)
                ->orWhere('course_code', 'like', $searchTerm)
                ->orWhere('national_code', 'like', $searchTerm);
        });
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    public function scopeFilterStatus($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value === 1) {
            return $query->where('activated_now', 1);
        } elseif ($value === 0) {
            return $query->where('activated_now', 0);
        }
    }
}
