<?php

namespace App\Model\Traits;

use App\Model\Staff;
use App\Model\v2\Student;

trait RegisterImprovementFilterTrait
{
    public function staffUser()
    {
        return $this->belongsTo(Staff::class, 'requested_by');
    }

    public function studentUser()
    {
        return $this->belongsTo(Student::class, 'requested_by', 'generated_stud_id');
    }

    public static function getCategories()
    {
        return self::pluck('category', 'category')->toArray();
    }

    public function scopeFilterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('action_taken', 'like', $searchTerm)
                ->orWhere('action_taken_prevent', 'like', $searchTerm)
                ->orWhere('case_detail', 'like', $searchTerm);
        });
    }

    public function scopeFilterCategory($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value == 'all') {
            return $query;
        }

        return $query->where('category', $value);
    }

    public function scopeFilterStatus($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value == 'all') {
            return $query;
        }

        return $query->where('status', $value);
    }

    public function scopeFilterLoggedBy($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value == 'all') {
            return $query;
        }

        return $query->where('logged_by', $value);
    }

    public function scopeFilterRequestedBy($query, $value)
    {
        if (empty($value)) {
            return $query;
        }
        if ($value == 'all') {
            return $query;
        }

        return $query->where('requested_by', $value);
    }
}
