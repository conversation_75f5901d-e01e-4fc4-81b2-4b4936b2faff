<?php

namespace App\Model\Traits;

trait SetupServicesFilterTrait
{
    public function scopeFilterQuery($query, $value)
    {
        if (! $value) {
            return $query;
        }

        return $query->likeWhere(['facility_name'], $value);
    }

    public function scopeFilterServicesNameId(
        $query,
        $id
    ) {
        if (! $id) {
            return $query;
        }

        return $query->where('services_name_id', $id);
    }

    public function scopeFilterCategoryId(
        $query,
        $id
    ) {
        if (! $id) {
            return $query;
        }

        return $query->where('category_id', $id);
    }

    public function scopeFilterIsActive(
        $query,
        $id
    ) {
        if (! $id) {
            return $query;
        }

        return $query->where('is_active', $id);
    }
}
