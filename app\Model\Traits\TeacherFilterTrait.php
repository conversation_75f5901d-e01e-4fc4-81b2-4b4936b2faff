<?php

namespace App\Model\Traits;

use App\Model\v2\Users;

trait TeacherFilterTrait
{
    public function scopeFilterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name_title', 'like', $searchTerm)
                ->orWhere('first_name', 'like', $searchTerm)
                ->orWhere('last_name', 'like', $searchTerm)
                ->orWhere('email', 'like', $searchTerm)
                ->orWhere('personal_email', 'like', $searchTerm)
                ->orWhere('phone', 'like', $searchTerm)
                ->orWhere('mobile', 'like', $searchTerm)
                ->orWhere('staff_number', 'like', $searchTerm)
                ->orWhere('position', 'like', $searchTerm);
        });
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    public function users()
    {
        return $this->hasOne(Users::class, 'id', 'user_id');
    }
}
