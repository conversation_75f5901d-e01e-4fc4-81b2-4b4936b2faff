<?php

namespace App\Model\Traits;

use App\Model\Courses;
use App\Model\Teacher;
use App\Model\v2\Subject;

trait TeacherMatrixFilterTrait
{
    public function scopeFilterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('knowledge_level', 'like', $searchTerm);
        });
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id', 'id');
    }

    public function teacher()
    {
        return $this->belongsTo(Teacher::class, 'teacher_id', 'id');
    }

    public function course()
    {
        return $this->belongsTo(Courses::class, 'course_id', 'id');
    }
}
