<?php

namespace App\Model\v2;

use App\Model\Traits\SetupServicesFilterTrait;
use Illuminate\Database\Eloquent\Model;

class SetupServices extends Model
{
    use SetupServicesFilterTrait;

    protected $table = 'rto_services_setup';

    protected $fillable = [
        'college_id',
        'services_name_id',
        'category_id',
        'facility_name',
        'facility_price_type',
        'student_price',
        'is_active',
        'created_by',
        'updated_by',
    ];

    public function college()
    {
        return $this->belongsTo(Colleges::class, 'college_id');
    }

    public function serviceName()
    {
        return $this->belongsTo(SetupServicesName::class, 'services_name_id');
    }

    public function category()
    {
        return $this->belongsTo(SetupServicesCategory::class, 'category_id');
    }

    // legacy code:
    // todo: if not in use remove it
    public function addServicesSetup($collegeId, $userId, $request)
    {
        $objServicesSetup = new SetupServices;
        $objServicesSetup->college_id = $collegeId;
        $objServicesSetup->services_name_id = $request->input('services_name');
        $objServicesSetup->category_id = $request->input('category_name');
        $objServicesSetup->facility_name = $request->input('facility_name');
        $objServicesSetup->facility_price_type = $request->input('facility_price_type');
        $objServicesSetup->student_price = $request->input('student_price');
        $objServicesSetup->is_active = '1';
        $objServicesSetup->created_by = $userId;
        $objServicesSetup->updated_by = $userId;
        $objServicesSetup->save();

        return true;
    }

    public function updateServicesNameV2($collegeId, $userId, $resultArr)
    {
        $countResult = SetupServices::where('college_id', $collegeId)
            ->where('id', '!=', $resultArr['servicesId'])
            ->where('facility_name', $resultArr['facilityName'])
            ->count();
        $objServeEdit = SetupServices::find($resultArr['servicesId']);
        $objServeEdit->facility_name = $resultArr['facilityName'];
        $objServeEdit->student_price = $resultArr['studentPrice'];
        $objServeEdit->created_by = $userId;
        $objServeEdit->updated_by = $userId;
        if ($countResult == 0) {
            $objServeEdit->save();
            $returnData['type'] = 'alert-success';
            $returnData['message'] = 'Services Facility Updated Successfully.';
        } else {
            $returnData['type'] = 'alert-error';
            $returnData['message'] = 'Services Name already exist';
        }

        return $returnData;
    }

    public function getServicesName($college_id)
    {
        $resultArr = SetupServices::where('college_id', $college_id)->get()->pluck('services_name', 'id')->toArray();
        $newArr = ['Add' => 'Add New Services Name'];
        if (count($resultArr) > 0) {
            return $resultArr + $newArr;
        } else {
            $result = ['' => 'No Data Found'];

            return $result + $newArr;
        }
    }

    public function getFacilityName($college_id, $arrResult)
    {
        //        print_r($arrResult);exit;
        $resultArr = SetupServices::where('college_id', $college_id)
            ->where('is_active', '1')
            ->where('category_id', $arrResult['categoryId'])
            ->get()->pluck('facility_name', 'id')->toArray();
        if (count($resultArr) > 0) {
            return $resultArr;
        } else {
            $result = ['' => 'No Data Found'];

            return $result;
        }
    }

    public function getServicesList($collegeId, $resultArr)
    {
        //        print_r($resultArr);
        //        exit;
        $sql = SetupServices::leftjoin('rto_setup_services_category', 'rto_setup_services_category.id', '=', 'rto_services_setup.category_id')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rto_services_setup.services_name_id')
            ->where('rto_services_setup.college_id', '=', $collegeId);

        //        if (!empty($resultArr['services_name'])) {
        //            $sql->where('rto_services_setup.services_name_id', $resultArr['services_name']);
        //        }
        //        if (!empty($resultArr['category_name'])) {
        //            $sql->where('rto_services_setup.category_id', $resultArr['category_name']);
        //        }
        if ($resultArr['active_radio'] != '3') {
            $sql->where('rto_services_setup.is_active', $resultArr['active_radio']);
        }

        $resultArr = $sql->get(['rto_services_setup.*',
            'rto_services_setup.is_active as activeStatus',
            'rto_setup_services_name.services_name',
            'rto_setup_services_name.date_type',
            'rto_setup_services_name.is_allow_student',
            'rto_setup_services_name.is_internal_only',
            'rto_setup_services_category.category_name']);

        //        print_r($resultArr);exit;
        return $resultArr;
    }

    public function changeStatus($collegeId, $data)
    {
        $objChangeStatus = SetupServices::find($data['servicesId']);
        $objChangeStatus->is_active = ($data['status'] == 1) ? 0 : 1;
        $objChangeStatus->save();
    }

    public function getUpdateCategoryData($collegeId, $resultArr)
    {
        //        print_r($resultArr);
        //        exit;
        $sql = SetupServices::leftjoin('rto_setup_services_category', 'rto_setup_services_category.id', '=', 'rto_services_setup.category_id')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rto_services_setup.services_name_id')
            ->where('rto_services_setup.college_id', '=', $collegeId)
            ->where('rto_services_setup.id', '=', $resultArr['servicesId']);

        $resultArr = $sql->get(['rto_services_setup.*',
            'rto_setup_services_name.services_name',
            'rto_setup_services_name.date_type',
            'rto_setup_services_name.is_allow_student',
            'rto_setup_services_name.is_internal_only',
            'rto_setup_services_category.category_name']);

        return $resultArr;
    }

    public function deleteService($resultArr)
    {
        $deleteId = $resultArr['deleteId'];
        SetupServices::where('id', $deleteId)->delete();
        $returnData['type'] = 'alert-success';
        $returnData['message'] = 'Services delete Successfully.';

        return $returnData;
    }

    public function _getAvailableFacilityHTML($collegeId, $resultArr, $dacilityId)
    {
        $dacilityId = explode(',', $dacilityId);
        $sql = SetupServices::leftjoin('rto_setup_services_category', 'rto_setup_services_category.id', '=', 'rto_services_setup.category_id')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rto_services_setup.services_name_id')
            ->where('rto_services_setup.college_id', '=', $collegeId);
        $sql->where('rto_services_setup.services_name_id', $resultArr['services_name']);
        $sql->where('rto_services_setup.category_id', $resultArr['services_category']);
        if (! empty($dacilityId)) {
            $sql->whereNotIn('rto_services_setup.id', $dacilityId);
        }
        $resultArr = $sql->get(['rto_services_setup.*',
            'rto_services_setup.is_active as activeStatus',
            'rto_setup_services_name.services_name',
            'rto_setup_services_name.date_type',
            'rto_setup_services_name.is_allow_student',
            'rto_setup_services_name.is_internal_only',
            'rto_setup_services_category.category_name']);

        //        echo '<pre/>'; print_r($resultArr);exit;
        return $resultArr;
    }
}
