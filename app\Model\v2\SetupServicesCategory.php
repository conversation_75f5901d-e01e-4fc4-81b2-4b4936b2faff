<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;

class SetupServicesCategory extends Model
{
    protected $table = 'rto_setup_services_category';

    protected $fillable = ['id', 'college_id', 'service_name_id', 'category_name', 'created_by', 'updated_by'];

    public function college()
    {
        return $this->belongsTo(Colleges::class, 'college_id', 'id');
    }

    public function serviceName()
    {
        return $this->belongsTo(SetupServicesName::class, 'service_name_id', 'id');
    }

    public function addCategory($collegeId, $userId, $data)
    {

        $countResult = SetupServicesCategory::where('college_id', $collegeId)->where('category_name', $data['category_name'])->count();

        $objServices = new SetupServicesCategory;
        $objServices->college_id = $collegeId;
        $objServices->category_name = $data['category_name'];
        $objServices->service_name_id = $data['services_name'];
        $objServices->created_by = $userId;
        $objServices->updated_by = $userId;
        if ($countResult == 0) {
            $objServices->save();
            $returnData['type'] = 'alert-success';
            $returnData['message'] = 'Services Category Saved Successfully.';
            $returnData['lastId'] = $objServices->id;
        } else {
            $returnData['type'] = 'alert-error';
            $returnData['message'] = 'Services Category already exist';
        }

        return $returnData;
    }

    public function updateCategory($collegeId, $userId, $data)
    {

        $countResult = SetupServicesCategory::where('college_id', $collegeId)
            ->where('id', '!=', $data['categoryId'])
            ->where('category_name', $data['categoryName'])
            ->count();

        $objServices = SetupServicesCategory::find($data['categoryId']);
        $objServices->college_id = $collegeId;
        $objServices->category_name = $data['categoryName'];
        $objServices->created_by = $userId;
        $objServices->updated_by = $userId;
        if ($countResult == 0) {
            $objServices->save();
            $returnData['type'] = 'alert-success';
            $returnData['message'] = 'Services Category Update Successfully.';
            $returnData['lastId'] = $objServices->id;
        } else {
            $returnData['type'] = 'alert-error';
            $returnData['message'] = 'Services Category already exist';
        }

        return $returnData;
    }

    public function getCategoryName($college_id, $resultArr)
    {
        $resultArr = SetupServicesCategory::where('college_id', $college_id)
            ->where('service_name_id', $resultArr['servicesId'])
            ->get()->pluck('category_name', 'id')->toArray();
        $newArr = ['Add' => 'Add New category Name'];
        if (count($resultArr) > 0) {
            return $resultArr + $newArr;
        } else {
            $result = ['' => 'No Data Found'];

            return $result + $newArr;
        }
    }

    public function getCategoryNamev2($college_id, $resultArr)
    {
        $resultArr = SetupServicesCategory::where('college_id', $college_id)
            ->where('service_name_id', $resultArr['servicesId'])
            ->get()->pluck('category_name', 'id')->toArray();
        if (count($resultArr) > 0) {
            return $resultArr;
        } else {
            $result = ['' => 'No Data Found'];

            return $result;
        }
    }
}
