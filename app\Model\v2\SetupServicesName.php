<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;

class SetupServicesName extends Model
{
    protected $table = 'rto_setup_services_name';

    protected $fillable = [
        'college_id',
        'services_name',
        'date_type',
        'is_allow_student',
        'is_internal_only',
        'created_by',
        'updated_by',
    ];

    public function college()
    {
        return $this->belongsTo(Colleges::class, 'college_id', 'id');
    }

    // legacy code:
    // todo: if not in use remove it
    public function addServices($collegeId, $userId, $data)
    {

        $countResult = SetupServicesName::where('college_id', $collegeId)->where('services_name', $data['addServicesName'])->count();

        $objServices = new SetupServicesName;
        $objServices->college_id = $collegeId;
        $objServices->services_name = $data['addServicesName'];
        $objServices->date_type = $data['dateType'];
        $objServices->is_allow_student = $data['allowStudent'];
        $objServices->is_internal_only = $data['internalOnly'];
        $objServices->created_by = $userId;
        $objServices->updated_by = $userId;
        if ($countResult == 0) {
            $objServices->save();
            $returnData['type'] = 'alert-success';
            $returnData['message'] = 'Services Name Saved Successfully.';
            $returnData['lastId'] = $objServices->id;
        } else {
            $returnData['type'] = 'alert-error';
            $returnData['message'] = 'Services Name already exist';
        }

        return $returnData;
    }

    public function updateServicesName($collegeId, $userId, $resultArr)
    {
        $countResult = SetupServicesName::where('college_id', $collegeId)
            ->where('id', '!=', $resultArr['servicesId'])
            ->where('services_name', $resultArr['servicesName'])
            ->count();
        $objServeEdit = SetupServicesName::find($resultArr['servicesId']);
        $objServeEdit->services_name = $resultArr['servicesName'];
        $objServeEdit->is_allow_student = $resultArr['allowStudent'];
        $objServeEdit->is_internal_only = $resultArr['internalOnly'];
        $objServeEdit->date_type = $resultArr['dateType'];
        $objServeEdit->created_by = $userId;
        $objServeEdit->updated_by = $userId;
        if ($countResult == 0) {
            $objServeEdit->save();
            $returnData = 'success';
        } else {
            $returnData = 'error';
        }

        return $returnData;
    }

    public function getServicesName($college_id)
    {
        $resultArr = SetupServicesName::where('college_id', $college_id)->get()->pluck('services_name', 'id')->toArray();
        $newArr = ['Add' => 'Add New Services Name'];
        if (count($resultArr) > 0) {
            return $resultArr + $newArr;
        } else {
            $result = ['' => 'No Data Found'];

            return $result + $newArr;
        }
    }

    public function getServicesNameV2($college_id)
    {
        $resultArr = SetupServicesName::where('college_id', $college_id)->get()->pluck('services_name', 'id')->toArray();
        if (count($resultArr) > 0) {
            return $resultArr;
        } else {
            return ['' => 'No Data Found'];
        }
    }
}
