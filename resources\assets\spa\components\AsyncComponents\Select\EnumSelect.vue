<script setup>
import { computed, onMounted, ref } from 'vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import DropdownMultiSelect from '@spa/components/Dropdown/DropdownMultiSelect.vue';
import apiClient from '@spa/services/api.client.js';

const props = defineProps({
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'value',
    },
    optionLabel: {
        type: String,
        default: 'label',
    },
    disabled: Boolean,
    enumClass: String,
    modelValue: {},
    clearable: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['update:modelValue']);

const allOptions = ref([]);
const loading = ref(false);
const defaultItem = computed(() => ({
    [props.optionLabel]: props.placeholder,
    [props.optionValue]: null,
}));

onMounted(async () => {
    fetchEnumOptions();
});

const computedValue = computed({
    get() {
        return props.modelValue ?? '';
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const fetchEnumOptions = async () => {
    try {
        const { data } = await apiClient.get(
            `/api/v2/tenant/utilities/get-enum-options?enum=${props.enumClass}`
        );
        let allOption = {};
        allOption[props.optionValue] = '';
        allOption[props.optionLabel] = 'All';
        data.unshift(allOption);
        allOptions.value = data;
    } catch (e) {
        console.error('Error fetching options:', e);
        allOptions.value = [];
    }
};
</script>

<template>
    <div :class="`async-select ${className}`">
        <DropdownMultiSelect
            v-if="multiple"
            :data-items="allOptions"
            :text-field="optionLabel"
            :value-field="optionValue"
            :data-item-key="optionValue"
            :clear-button="clearable"
            :disabled="disabled"
            :readonly="readonly"
            :label="label"
            :loading="loading"
            :value-primitive="true"
            :default-item="defaultItem"
            v-model="computedValue"
            :style="{
                width: '100%',
                minWidth: '200px',
                maxWidth: '400px',
            }"
            :filterable="false"
        >
        </DropdownMultiSelect>
        <DropDownList
            v-else
            :style="{
                width: '100%',
                minWidth: '200px',
                maxWidth: '400px',
            }"
            :data-items="allOptions"
            :text-field="optionLabel"
            :value-field="optionValue"
            :data-item-key="optionValue"
            v-model="computedValue"
            :filterable="false"
            :clear-button="clearable"
            :disabled="disabled"
            :readonly="readonly"
            :label="label"
            :loading="loading"
            :default-item="defaultItem"
            :value-primitive="true"
        />
    </div>
</template>
<style lang="scss">
.async-select {
    .k-multiselect {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}
</style>
