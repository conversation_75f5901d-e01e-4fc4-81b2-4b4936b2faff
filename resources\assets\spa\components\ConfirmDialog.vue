<template>
    <k-dialog
        v-if="visible"
        append-to="body"
        :title="''"
        @close="onHide"
        :initial-height="200"
        :dialog-class="dialogClass"
        :width="dialogWidth"
    >
        <!-- <p class="text-sm text-gray-700" v-if="variant !== 'danger'">
            {{ message }}
        </p> -->
        <context-loader
            :context="'remove'"
            :type="'default'"
            :overlay="false"
            :pt="{ root: 'flex items-center justify-center h-full' }"
        >
            <template #loading>
                <div class="flex items-center justify-center">
                    <Spinner
                        :size="'3xl'"
                        :pt="{ root: 'items-center' }"
                        loadingText="Deleting..."
                    />
                </div>
            </template>
            <div class="flex gap-3">
                <div
                    class="grow-1 flex h-6 w-6 items-center justify-center rounded-full p-0.5"
                    :class="iconClass"
                >
                    <file-icon :name="variant ? variant : 'warning'" />
                </div>
                <div class="grow-1 space-y-2">
                    <h3 class="text-lg font-medium text-gray-800">{{ header }}</h3>
                    <p class="text-sm text-gray-600">{{ message }}</p>
                </div>
            </div>
        </context-loader>
        <dialog-actions-bar v-if="!store.isContextLoading('remove')">
            <div class="flex w-full justify-end gap-4 px-4 py-2">
                <Button
                    variant="secondary"
                    :icon="rejectIcon"
                    :class="rejectClass"
                    :size="'sm'"
                    @click="reject()"
                    :autofocus="autoFocusReject"
                >
                    <div class="text-gray-700">{{ rejectLabel }}</div>
                </Button>
                <Button
                    :variant="variant === 'danger' ? 'danger' : 'primary'"
                    :icon="acceptIcon"
                    :class="acceptClass"
                    @click="accept()"
                    :size="'sm'"
                    :autofocus="autoFocusAccept"
                >
                    <div class="text-white">{{ acceptLabel }}</div>
                </Button>
            </div>
        </dialog-actions-bar>
    </k-dialog>
</template>

<script>
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import Spinner from '@spa/components/Loader/Spinner.vue';
import { twMerge } from 'tailwind-merge';

export default {
    name: 'ConfirmDialog',
    setup() {
        const store = useLoaderStore();
        return {
            store,
        };
    },
    components: {
        'k-dialog': Dialog,
        'dialog-actions-bar': DialogActionsBar,
        Button,
        'context-loader': GlobalContextLoader,
        Spinner,
    },
    props: {
        group: { type: String, default: 'confirm' },
        breakpoints: {
            type: Object,
            default: null,
        },
        draggable: {
            type: Boolean,
            default: true,
        },
    },
    confirmListener: null,
    closeListener: null,
    data() {
        return {
            visible: false,
            confirmation: null,
        };
    },
    mounted() {
        this.confirmListener = (options) => {
            if (!options) {
                return;
            }
            // console.log(options);

            if (options.group === this.group) {
                this.confirmation = options;

                // console.log(this.confirmation);

                if (this.confirmation.onShow) {
                    this.confirmation.onShow();
                }

                this.visible = true;
            }
        };

        this.closeListener = () => {
            this.visible = false;
            this.confirmation = null;
        };

        window['Fire'].on('confirm_open', this.confirmListener);
        window['Fire'].on('confirm_close', this.closeListener);
        window.addEventListener('keydown', this.handleKeyPress);
    },
    beforeUnmount() {
        window['Fire'].off('confirm_open', this.confirmListener);
        window['Fire'].off('confirm_close', this.closeListener);
    },
    methods: {
        accept() {
            if (this.confirmation?.accept) {
                this.confirmation.accept();
            }
            this.store.startContextLoading('remove');

            // this.visible = false;
        },
        reject() {
            if (this.confirmation?.reject) {
                this.confirmation.reject();
            }

            this.visible = false;
        },
        onHide() {
            if (this.confirmation?.onHide) {
                this.confirmation.onHide();
            }

            this.visible = false;
        },
        handleKeyPress(event) {
            if (event.key === 'Enter' || event.keyCode === 13) {
                this.accept();
            } else if (event.key === 'Escape' || event.keyCode === 27) {
                this.reject();
            }
        },
    },
    computed: {
        header() {
            return this.confirmation ? this.confirmation.header : null;
        },
        message() {
            return this.confirmation ? this.confirmation.message : null;
        },
        blockScroll() {
            return this.confirmation ? this.confirmation.blockScroll : true;
        },
        position() {
            return this.confirmation ? this.confirmation.position : null;
        },
        // iconClass() {
        //     return ['p-confirm-dialog-icon', this.confirmation ? this.confirmation.icon : null];
        // },
        acceptLabel() {
            return this.confirmation ? this.confirmation.acceptLabel || 'Yes' : null;
        },
        rejectLabel() {
            return this.confirmation ? this.confirmation.rejectLabel || 'No' : null;
        },
        acceptIcon() {
            return this.confirmation ? this.confirmation.acceptIcon : null;
        },
        rejectIcon() {
            return this.confirmation ? this.confirmation.rejectIcon : null;
        },
        acceptClass() {
            return [
                'p-confirm-dialog-accept',
                this.confirmation ? this.confirmation.acceptClass : null,
            ];
        },
        rejectClass() {
            return [
                'p-confirm-dialog-reject',
                this.confirmation ? this.confirmation.rejectClass || 'p-button-text' : null,
            ];
        },
        autoFocusAccept() {
            return this.confirmation.defaultFocus === undefined ||
                this.confirmation.defaultFocus === 'accept'
                ? true
                : false;
        },
        autoFocusReject() {
            return this.confirmation.defaultFocus === 'reject' ? true : false;
        },
        closeOnEscape() {
            return this.confirmation ? this.confirmation.closeOnEscape : true;
        },
        variant() {
            return this.confirmation ? this.confirmation.variant : 'dialog';
        },
        dialogWidth() {
            return this.confirmation ? this.confirmation.width : 400;
        },
        iconClass() {
            let classMappings = {
                warning: 'bg-yellow-50',
                danger: 'bg-red-50',
                info: 'bg-blue-50',
                success: 'bg-green-50',
            };
            return classMappings[this.variant] || 'bg-yellow-50';
        },
        dialogClass() {
            return twMerge(
                'tw-dialog custom-modal-wrapper',
                this.variant === 'danger' ? 'tw-dialog--danger' : ''
            );
        },
        removeLoading() {
            return this.store.contextLoaders.remove || false;
        },
    },
    watch: {
        removeLoading(newVal) {
            if (!newVal) {
                this.visible = false;
            }
        },
    },
};
</script>
