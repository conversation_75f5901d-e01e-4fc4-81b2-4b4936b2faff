<template>
    <svg
        v-if="name == 'time'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2ZM10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3ZM9.5 5C9.74546 5 9.94961 5.17688 9.99194 5.41012L10 5.5V10H12.5C12.7761 10 13 10.2239 13 10.5C13 10.7455 12.8231 10.9496 12.5899 10.9919L12.5 11H9.5C9.25454 11 9.05039 10.8231 9.00806 10.5899L9 10.5V5.5C9 5.22386 9.22386 5 9.5 5Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-if="name == 'filter'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M2 3C2 2.44772 2.44772 2 3 2H13C13.5523 2 14 2.44772 14 3V4.25245C14 4.51767 13.8946 4.77202 13.7071 4.95956L9.62623 9.04044C9.43869 9.22798 9.33333 9.48233 9.33333 9.74755V11.3333L6.66667 14V9.74755C6.66667 9.48233 6.56131 9.22798 6.37377 9.04044L2.29289 4.95956C2.10536 4.77202 2 4.51767 2 4.25245V3Z"
            stroke="#9CA3AF"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'calendar'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M7 11C7.55228 11 8 10.5523 8 10C8 9.44771 7.55228 9 7 9C6.44772 9 6 9.44771 6 10C6 10.5523 6.44772 11 7 11ZM8 13C8 13.5523 7.55228 14 7 14C6.44772 14 6 13.5523 6 13C6 12.4477 6.44772 12 7 12C7.55228 12 8 12.4477 8 13ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44771 10.5523 9 10 9C9.44771 9 9 9.44771 9 10C9 10.5523 9.44771 11 10 11ZM11 13C11 13.5523 10.5523 14 10 14C9.44771 14 9 13.5523 9 13C9 12.4477 9.44771 12 10 12C10.5523 12 11 12.4477 11 13ZM13 11C13.5523 11 14 10.5523 14 10C14 9.44771 13.5523 9 13 9C12.4477 9 12 9.44771 12 10C12 10.5523 12.4477 11 13 11ZM17 5.5C17 4.11929 15.8807 3 14.5 3H5.5C4.11929 3 3 4.11929 3 5.5V14.5C3 15.8807 4.11929 17 5.5 17H14.5C15.8807 17 17 15.8807 17 14.5V5.5ZM4 7H16V14.5C16 15.3284 15.3284 16 14.5 16H5.5C4.67157 16 4 15.3284 4 14.5V7ZM5.5 4H14.5C15.3284 4 16 4.67157 16 5.5V6H4V5.5C4 4.67157 4.67157 4 5.5 4Z"
            fill="#9CA3AF"
        />
    </svg>

    <svg
        v-else-if="name == 'note'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <g clip-path="url(#clip0_4036_1136)">
            <path
                d="M8.87305 3.5C8.87305 5.98528 6.85833 8 4.37305 8C1.88777 8 -0.126953 5.98528 -0.126953 3.5C-0.126953 1.01472 1.88777 -1 4.37305 -1C6.85833 -1 8.87305 1.01472 8.87305 3.5ZM4.87305 1.5C4.87305 1.22386 4.64919 1 4.37305 1C4.0969 1 3.87305 1.22386 3.87305 1.5V3H2.37305C2.0969 3 1.87305 3.22386 1.87305 3.5C1.87305 3.77614 2.0969 4 2.37305 4H3.87305L3.87305 5.5C3.87305 5.77614 4.0969 6 4.37305 6C4.64919 6 4.87305 5.77614 4.87305 5.5V4H6.37305C6.64919 4 6.87305 3.77614 6.87305 3.5C6.87305 3.22386 6.64919 3 6.37305 3H4.87305V1.5ZM12.873 2H9.66601C9.56741 1.65136 9.43528 1.31679 9.27334 1H12.873C14.4707 1 15.7767 2.24892 15.868 3.82373L15.873 4V8.37868C15.873 8.85018 15.7066 9.30451 15.4061 9.66307L15.2873 9.79289L10.6659 14.4142C10.3325 14.7476 9.89356 14.9511 9.42758 14.9923L9.25173 15H4.87305C3.27537 15 1.96939 13.7511 1.87814 12.1763L1.87305 12V8.40029C2.18984 8.56223 2.52441 8.69436 2.87305 8.79297V12C2.87305 13.0544 3.68892 13.9182 4.72378 13.9945L4.87305 14H8.87305V11C8.87305 9.40232 10.122 8.09634 11.6968 8.00509L11.873 8H14.873V4C14.873 2.94564 14.0572 2.08183 13.0223 2.00549L12.873 2ZM14.6555 9.00134L11.873 9C10.8187 9 9.95488 9.81588 9.87853 10.8507L9.87305 11V13.781L9.95883 13.7071L14.5802 9.08579C14.607 9.05895 14.6321 9.03075 14.6555 9.00134Z"
                :fill="fill"
            />
        </g>
        <defs>
            <clipPath id="clip0_4036_1136">
                <rect width="16" height="16" fill="white" transform="translate(0.873047)" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'draghandle'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :class="className"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M7 5C7.55228 5 8 4.55228 8 4C8 3.44772 7.55228 3 7 3C6.44772 3 6 3.44772 6 4C6 4.55228 6.44772 5 7 5ZM7 11C7.55228 11 8 10.5523 8 10C8 9.44771 7.55228 9 7 9C6.44772 9 6 9.44771 6 10C6 10.5523 6.44772 11 7 11ZM8 16C8 16.5523 7.55228 17 7 17C6.44772 17 6 16.5523 6 16C6 15.4477 6.44772 15 7 15C7.55228 15 8 15.4477 8 16ZM13 5C13.5523 5 14 4.55228 14 4C14 3.44772 13.5523 3 13 3C12.4477 3 12 3.44772 12 4C12 4.55228 12.4477 5 13 5ZM14 10C14 10.5523 13.5523 11 13 11C12.4477 11 12 10.5523 12 10C12 9.44771 12.4477 9 13 9C13.5523 9 14 9.44771 14 10ZM13 17C13.5523 17 14 16.5523 14 16C14 15.4477 13.5523 15 13 15C12.4477 15 12 15.4477 12 16C12 16.5523 12.4477 17 13 17Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'pencil' || name == 'edit'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M20.2677 3.73223L20.9748 3.02513V3.02513L20.2677 3.73223ZM6.5 21.0355V22.0355C6.76522 22.0355 7.01957 21.9301 7.20711 21.7426L6.5 21.0355ZM3 21.0355H2C2 21.5878 2.44772 22.0355 3 22.0355V21.0355ZM3 17.4644L2.29289 16.7573C2.10536 16.9448 2 17.1992 2 17.4644H3ZM17.4393 4.43934C18.0251 3.85355 18.9748 3.85355 19.5606 4.43934L20.9748 3.02513C19.608 1.65829 17.3919 1.65829 16.0251 3.02513L17.4393 4.43934ZM19.5606 4.43934C20.1464 5.02513 20.1464 5.97487 19.5606 6.56066L20.9748 7.97487C22.3417 6.60804 22.3417 4.39196 20.9748 3.02513L19.5606 4.43934ZM19.5606 6.56066L5.79289 20.3284L7.20711 21.7426L20.9748 7.97487L19.5606 6.56066ZM6.5 20.0355H3V22.0355H6.5V20.0355ZM16.0251 3.02513L2.29289 16.7573L3.70711 18.1715L17.4393 4.43934L16.0251 3.02513ZM2 17.4644V21.0355H4V17.4644H2ZM14.5251 5.93934L18.0606 9.47487L19.4748 8.06066L15.9393 4.52513L14.5251 5.93934Z"
            :fill="fill"
        />
    </svg>

    <svg
        v-else-if="name == 'cancel' || name == 'cross'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'dashline'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M5 10C5 9.44772 5.44772 9 6 9L14 9C14.5523 9 15 9.44772 15 10C15 10.5523 14.5523 11 14 11L6 11C5.44772 11 5 10.5523 5 10Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'options'"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M5.25 8C5.25 8.69036 4.69036 9.25 4 9.25C3.30964 9.25 2.75 8.69036 2.75 8C2.75 7.30964 3.30964 6.75 4 6.75C4.69036 6.75 5.25 7.30964 5.25 8ZM9.25 8C9.25 8.69036 8.69036 9.25 8 9.25C7.30964 9.25 6.75 8.69036 6.75 8C6.75 7.30964 7.30964 6.75 8 6.75C8.69036 6.75 9.25 7.30964 9.25 8ZM12 9.25C12.6904 9.25 13.25 8.69036 13.25 8C13.25 7.30964 12.6904 6.75 12 6.75C11.3096 6.75 10.75 7.30964 10.75 8C10.75 8.69036 11.3096 9.25 12 9.25Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'plus' || name == 'add'"
        :width="width"
        :height="height"
        :class="className"
        viewBox="0 0 16 16"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M7.99844 2.40002C8.44027 2.40002 8.79844 2.7582 8.79844 3.20002V7.20002H12.7984C13.2403 7.20002 13.5984 7.5582 13.5984 8.00002C13.5984 8.44185 13.2403 8.80002 12.7984 8.80002H8.79844V12.8C8.79844 13.2419 8.44027 13.6 7.99844 13.6C7.55661 13.6 7.19844 13.2419 7.19844 12.8V8.80002H3.19844C2.75661 8.80002 2.39844 8.44185 2.39844 8.00002C2.39844 7.5582 2.75661 7.20002 3.19844 7.20002H7.19844V3.20002C7.19844 2.7582 7.55661 2.40002 7.99844 2.40002Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 're-sync'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <rect width="24" height="24" rx="5" fill="#EF4444" />
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M17.8069373,7 C16.4464601,5.07869636 14.3936238,4 12,4 C7.581722,4 4,7.581722 4,12 L2,12 C2,6.4771525 6.4771525,2 12,2 C14.8042336,2 17.274893,3.18251178 19,5.27034886 L19,4 L21,4 L21,9 L16,9 L16,7 L17.8069373,7 Z M6.19306266,17 C7.55353989,18.9213036 9.60637619,20 12,20 C16.418278,20 20,16.418278 20,12 L22,12 C22,17.5228475 17.5228475,22 12,22 C9.19576641,22 6.72510698,20.8174882 5,18.7296511 L5,20 L3,20 L3,15 L8,15 L8,17 L6.19306266,17 Z M12.0003283,15.9983464 C11.4478622,15.9983464 11,15.5506311 11,14.9983464 C11,14.4460616 11.4478622,13.9983464 12.0003283,13.9983464 C12.5527943,13.9983464 13.0006565,14.4460616 13.0006565,14.9983464 C13.0006565,15.5506311 12.5527943,15.9983464 12.0003283,15.9983464 Z M11.0029544,6.99834639 L13.0036109,6.99834639 L13.0036109,12.9983464 L11.0029544,12.9983464 L11.0029544,6.99834639 Z"
            fill="white"
        />
    </svg>
    <svg
        v-else-if="name == 'uparrow'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M5 10L12 3M12 3L19 10M12 3V21"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'rightarrow'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M14 5L21 12M21 12L14 19M21 12L3 12"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'downarrow'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M19 14L12 21M12 21L5 14M12 21L12 3"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'leftarrow'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M10 19L3 12M3 12L10 5M3 12L21 12"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'tabsdividerright'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <g clip-path="url(#clip0_4323_15917)">
            <path d="M1 -2L19.1818 35.8L1 73.6" stroke="#D1D5DB" stroke-linejoin="round" />
        </g>
        <defs>
            <clipPath id="clip0_4323_15917">
                <rect width="20" height="72" fill="white" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'fileempty'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M5.5 3C4.11929 3 3 4.11929 3 5.5V14.5C3 15.8807 4.11929 17 5.5 17H14.5C15.8807 17 17 15.8807 17 14.5V8.82843C17 8.16539 16.7366 7.5295 16.2678 7.06066L12.9393 3.73223C12.4705 3.26339 11.8346 3 11.1716 3H5.5ZM4 5.5C4 4.67157 4.67157 4 5.5 4H11V7.5C11 8.32843 11.6716 9 12.5 9H16V14.5C16 15.3284 15.3284 16 14.5 16H5.5C4.67157 16 4 15.3284 4 14.5V5.5ZM15.7505 8H12.5C12.2239 8 12 7.77614 12 7.5V4.24951C12.083 4.30446 12.1608 4.36793 12.2322 4.43934L15.5607 7.76777C15.6321 7.83918 15.6955 7.91705 15.7505 8Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'graduationcap'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M8.5063 3.40123C9.4313 2.87016 10.5687 2.87016 11.4937 3.40123L18.749 7.56664C18.9042 7.6558 19 7.82119 19 8.00026C19 8.17933 18.9042 8.34472 18.7489 8.43388L16 10.0121V14.5002C16 14.6328 15.9473 14.76 15.8536 14.8538L15.852 14.8554L15.8496 14.8577L15.8428 14.8644L15.8201 14.8861C15.801 14.9041 15.7741 14.929 15.7394 14.9599C15.6701 15.0215 15.5696 15.1069 15.4389 15.2081C15.1777 15.4103 14.7948 15.6763 14.2978 15.9414C13.3033 16.4718 11.8479 17.0002 10 17.0002C8.15211 17.0002 6.69675 16.4718 5.70221 15.9414C5.20518 15.6763 4.82226 15.4103 4.5611 15.2081C4.43043 15.1069 4.32994 15.0215 4.26059 14.9599C4.22591 14.929 4.19898 14.9041 4.17992 14.8861C4.07226 14.7822 4 14.654 4 14.5002V10.0121L2 8.86386L2 13.5002C2 13.7764 1.77614 14.0002 1.5 14.0002C1.22386 14.0002 1 13.7764 1 13.5002V8.00026C1 7.81088 1.10529 7.64606 1.26052 7.5612L8.5063 3.40123ZM11.4937 12.5992C10.5687 13.1303 9.43131 13.1303 8.50632 12.5992L5 10.5862V14.2774C5.04686 14.317 5.10469 14.3643 5.17327 14.4174C5.39649 14.5902 5.73232 14.8241 6.17279 15.0591C7.05325 15.5286 8.34789 16.0002 10 16.0002C11.6521 16.0002 12.9467 15.5286 13.8272 15.0591C14.2677 14.8241 14.6035 14.5902 14.8267 14.4174C14.8953 14.3643 14.9531 14.317 15 14.2774V10.5862L11.4937 12.5992ZM10.9958 4.26846C10.3791 3.91442 9.62086 3.91442 9.0042 4.26846L2.50423 8.00025L9.00421 11.732C9.62087 12.086 10.3791 12.086 10.9958 11.732L17.4958 8.00025L10.9958 4.26846Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'timer'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M7.5 2C7.22386 2 7 2.22386 7 2.5C7 2.77614 7.22386 3 7.5 3H11.5C11.7761 3 12 2.77614 12 2.5C12 2.22386 11.7761 2 11.5 2H7.5ZM15.1563 3.92899C14.961 3.73373 14.6444 3.73373 14.4492 3.92899C14.2539 4.12426 14.2539 4.44084 14.4492 4.6361L15.8634 6.05031C16.0587 6.24558 16.3752 6.24558 16.5705 6.05031C16.7658 5.85505 16.7658 5.53847 16.5705 5.34321L15.1563 3.92899ZM9.5 6C9.22386 6 9 6.22386 9 6.5V11.5C9 11.7761 9.22386 12 9.5 12C9.77614 12 10 11.7761 10 11.5V6.5C10 6.22386 9.77614 6 9.5 6ZM9.5 18C13.366 18 16.5 14.866 16.5 11C16.5 7.13401 13.366 4 9.5 4C5.63401 4 2.5 7.13401 2.5 11C2.5 14.866 5.63401 18 9.5 18ZM9.5 17C6.18629 17 3.5 14.3137 3.5 11C3.5 7.68629 6.18629 5 9.5 5C12.8137 5 15.5 7.68629 15.5 11C15.5 14.3137 12.8137 17 9.5 17Z"
            :fill="fill"
        />
    </svg>

    <svg
        v-else-if="name == 'clock-simple'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M10.127 2C14.5452 2 18.127 5.58172 18.127 10C18.127 14.4183 14.5452 18 10.127 18C5.70867 18 2.12695 14.4183 2.12695 10C2.12695 5.58172 5.70867 2 10.127 2ZM10.127 3C6.26096 3 3.12695 6.13401 3.12695 10C3.12695 13.866 6.26096 17 10.127 17C13.9929 17 17.127 13.866 17.127 10C17.127 6.13401 13.9929 3 10.127 3ZM9.62695 5C9.87241 5 10.0766 5.17688 10.1189 5.41012L10.127 5.5V10H12.627C12.9031 10 13.127 10.2239 13.127 10.5C13.127 10.7455 12.9501 10.9496 12.7168 10.9919L12.627 11H9.62695C9.38149 11 9.17734 10.8231 9.13501 10.5899L9.12695 10.5V5.5C9.12695 5.22386 9.35081 5 9.62695 5Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'shielduser'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M10 9.49951C11.1046 9.49951 12 8.60408 12 7.49951C12 6.39494 11.1046 5.49951 10 5.49951C8.89543 5.49951 8 6.39494 8 7.49951C8 8.60408 8.89543 9.49951 10 9.49951ZM10 14.4995C12.5 14.4995 13.5 13.2447 13.5 11.9995C13.5 11.1711 12.8284 10.4995 12 10.4995H8C7.17157 10.4995 6.5 11.1711 6.5 11.9995C6.5 13.2494 7.5 14.4995 10 14.4995ZM10.2774 2.08397C10.1094 1.97201 9.8906 1.97201 9.72265 2.08397C7.78446 3.3761 5.68833 4.1823 3.42929 4.50503C3.18296 4.54021 3 4.75117 3 5V9.5C3 13.3913 5.30699 16.2307 9.82051 17.9667C9.93605 18.0111 10.064 18.0111 10.1795 17.9667C14.693 16.2307 17 13.3913 17 9.5V5C17 4.75117 16.817 4.54021 16.5707 4.50503C14.3117 4.1823 12.2155 3.3761 10.2774 2.08397ZM4 5.42787C5.98541 5.09055 7.85275 4.39606 9.59914 3.34583L10 3.09715L10.4009 3.34583C12.1473 4.39606 14.0146 5.09055 16 5.42787V9.5C16 12.892 14.0321 15.3634 10 16.9632C5.96795 15.3634 4 12.892 4 9.5V5.42787Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'checkcirc'"
        :width="width"
        :height="height"
        :fill="bgfill"
        :xmlns="xmlns"
        viewBox="0 0 24 24"
    >
        <path
            d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2Zm0 1.5a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17Zm-1.25 9.94 4.47-4.47a.75.75 0 0 1 1.133.976l-.073.084-5 5a.75.75 0 0 1-.976.073l-.084-.073-2.5-2.5a.75.75 0 0 1 .976-1.133l.084.073 1.97 1.97 4.47-4.47-4.47 4.47Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'actions'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M12 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4ZM12 14a2 2 0 1 1 0-4 2 2 0 0 1 0 4ZM10 18a2 2 0 1 0 4 0 2 2 0 0 0-4 0Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'dropdown'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M5.29289 7.29289C5.68342 6.90237 6.31658 6.90237 6.7071 7.29289L9.99999
        10.5858L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L10.7071
        12.7071C10.3166 13.0976 9.68341 13.0976 9.29289 12.7071L5.29289 8.70711C4.90237 8.31658 4.90237 7.68342 5.29289 7.29289Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'tickmark'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M16.7071 5.29289C17.0976 5.68342 17.0976 6.31658 16.7071 6.70711L8.70711 14.7071C8.31658 15.0976
        7.68342 15.0976 7.29289 14.7071L3.29289 10.7071C2.90237 10.3166 2.90237 9.68342 3.29289 9.29289C3.68342 8.90237 4.31658 8.90237 4.70711 9.29289L8
        12.5858L15.2929 5.29289C15.6834 4.90237 16.3166 4.90237 16.7071 5.29289Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'info'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2ZM10 3C6.13401 3 3 6.13401 3
        10C3 13.866 6.13401 17 10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3ZM10 13.5C10.4142 13.5 10.75 13.8358 10.75 14.25C10.75 14.6642
        10.4142 15 10 15C9.58579 15 9.25 14.6642 9.25 14.25C9.25 13.8358 9.58579 13.5 10 13.5ZM10 5.5C11.3807 5.5 12.5 6.61929 12.5 8C12.5 8.72959 12.1848
        9.40774 11.6513 9.8771L11.4967 10.0024L11.2782 10.1655L11.1906 10.2372C11.1348 10.2851 11.0835 10.3337 11.0346 10.3859C10.6963 10.7464 10.5 11.2422
        10.5 12C10.5 12.2761 10.2761 12.5 10 12.5C9.72386 12.5 9.5 12.2761 9.5 12C9.5 10.988 9.79312 10.2475 10.3054 9.70162C10.4165 9.5832 10.532 9.47988
        10.6609 9.37874L10.9076 9.19439L11.0256 9.09468C11.325 8.81435 11.5 8.42206 11.5 8C11.5 7.17157 10.8284 6.5 10 6.5C9.17157 6.5 8.5 7.17157 8.5 8C8.5
        8.27614 8.27614 8.5 8 8.5C7.72386 8.5 7.5 8.27614 7.5 8C7.5 6.61929 8.61929 5.5 10 5.5Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'lens'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M19 19L13 13M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        ></path>
    </svg>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        v-else-if="name == 'sync'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
    >
        <path
            :fill="fill"
            d="M16.25 5.18a.75.75 0 0 0 .142 1.051a7.251 7.251 0 0 1-3.599 12.976l.677-.677a.75.75 0 0 0-.977-1.133l-.084.073l-2 2a.75.75 0 0 0-.073.976l.073.084l2 2a.75.75 0 0 0 1.133-.976l-.072-.084l-.75-.75a8.75 8.75 0 0 0 4.581-15.68a.75.75 0 0 0-1.051.141m-5.72-3.71a.75.75 0 0 0 0 1.06l.75.75a8.75 8.75 0 0 0-4.85 15.47a.75.75 0 1 0 .956-1.157a7.251 7.251 0 0 1 3.82-12.8l-.676.677a.75.75 0 1 0 1.061 1.06l2-2a.75.75 0 0 0 0-1.06l-2-2a.75.75 0 0 0-1.06 0"
        />
    </svg>
    <svg
        v-else-if="name == 'import'"
        :width="width"
        :height="height"
        viewBox="0 0 10 13"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M0.5 12H9.5C9.77614 12 10 12.2239 10 12.5C10 12.7455 9.82312 12.9496 9.58988 12.9919L9.5 13H0.5C0.223858 13 0 12.7761 0 12.5C0 12.2545 0.176875 12.0504 0.410124 12.0081L0.5 12H9.5H0.5ZM4.91012 0.00805569L5 0C5.24546 0 5.44961 0.176875 5.49194 0.410124L5.5 0.5V9.292L8.18198 6.61091C8.35555 6.43735 8.62497 6.41806 8.81984 6.55306L8.88909 6.61091C9.06265 6.78448 9.08194 7.0539 8.94694 7.24877L8.88909 7.31802L5.35355 10.8536C5.17999 11.0271 4.91056 11.0464 4.71569 10.9114L4.64645 10.8536L1.11091 7.31802C0.915651 7.12276 0.915651 6.80617 1.11091 6.61091C1.28448 6.43735 1.5539 6.41806 1.74877 6.55306L1.81802 6.61091L4.5 9.292V0.5C4.5 0.25454 4.67688 0.0503916 4.91012 0.00805569L5 0L4.91012 0.00805569Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'location'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M5.843 4.568a8.707 8.707 0 1 1 12.314 12.314l-1.187 1.174c-.875.858-2.01 1.962-3.406 3.312a2.25 2.25 0 0 1-3.128 0l-3.491-3.396c-.439-.431-.806-.794-1.102-1.09a8.707 8.707 0 0 1 0-12.314Zm11.253 1.06A7.207 7.207 0 1 0 6.904 15.822L8.39 17.29a753.98 753.98 0 0 0 3.088 3 .75.75 0 0 0 1.043 0l3.394-3.3c.47-.461.863-.85 1.18-1.168a7.207 7.207 0 0 0 0-10.192ZM12 7.999a3.002 3.002 0 1 1 0 6.004 3.002 3.002 0 0 1 0-6.003Zm0 1.5a1.501 1.501 0 1 0 0 3.004 1.501 1.501 0 0 0 0-3.003Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'arrow-down'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M13.793 5.73271C14.0787 6.03263 14.0672 6.50737 13.7672 6.79306L8.51678 11.7944C8.22711 12.0703 7.77187 12.0703 7.4822 11.7944L2.23173 6.79306C1.93181 6.50737 1.92028 6.03263 2.20597 5.73271C2.49166 5.43279 2.96639 5.42125 3.26631 5.70694L7.99949 10.2155L12.7327 5.70694C13.0326 5.42125 13.5073 5.43279 13.793 5.73271Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'warning'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M10.0001 7.00016C10.2762 7.00016 10.5001 7.22402 10.5001 7.50016V11.5002C10.5001 11.7763 10.2762 12.0002 10.0001 12.0002C9.72392 12.0002 9.50006 11.7763 9.50006 11.5002V7.50016C9.50006 7.22402 9.72392 7.00016 10.0001 7.00016ZM10.0001 14.5002C10.4143 14.5002 10.7501 14.1644 10.7501 13.7502C10.7501 13.3359 10.4143 13.0002 10.0001 13.0002C9.58585 13.0002 9.25006 13.3359 9.25006 13.7502C9.25006 14.1644 9.58585 14.5002 10.0001 14.5002ZM8.68575 2.85232C9.25564 1.81616 10.7445 1.81615 11.3144 2.85232L17.8731 14.7773C18.423 15.777 17.6997 17.0002 16.5588 17.0002H3.44135C2.30044 17.0002 1.5772 15.777 2.12702 14.7773L8.68575 2.85232ZM10.4382 3.33424C10.2482 2.98885 9.75193 2.98885 9.56197 3.33424L3.00324 15.2592C2.81996 15.5924 3.06104 16.0002 3.44135 16.0002H16.5588C16.9391 16.0002 17.1802 15.5924 16.9969 15.2592L10.4382 3.33424Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'message' || name == 'mail'"
        :width="width"
        :height="height"
        :class="className"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M5.25 4h13.5a3.25 3.25 0 0 1 3.245 3.066L22 7.25v9.5a3.25 3.25 0 0 1-3.066 3.245L18.75 20H5.25a3.25 3.25 0 0 1-3.245-3.066L2 16.75v-9.5a3.25 3.25 0 0 1 3.066-3.245L5.25 4h13.5-13.5ZM20.5 9.373l-8.15 4.29a.75.75 0 0 1-.603.043l-.096-.042L3.5 9.374v7.376a1.75 1.75 0 0 0 1.606 1.744l.144.006h13.5a1.75 1.75 0 0 0 1.744-1.607l.006-.143V9.373ZM18.75 5.5H5.25a1.75 1.75 0 0 0-1.744 1.606L3.5 7.25v.429l8.5 4.473 8.5-4.474V7.25a1.75 1.75 0 0 0-1.607-1.744L18.75 5.5Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'checkmark-green'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="m8.5 16.586-3.793-3.793a1 1 0 0 0-1.414 1.414l4.5 4.5a1 1 0 0 0 1.414 0l11-11a1 1 0 0 0-1.414-1.414L8.5 16.586Z"
            fill="#10B981"
        />
    </svg>
    <svg
        v-else-if="name == 'close'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M4.39705 4.55379L4.46967 4.46967C4.73594 4.2034 5.1526 4.1792 5.44621 4.39705L5.53033 4.46967L12 10.939L18.4697 4.46967C18.7626 4.17678 19.2374 4.17678 19.5303 4.46967C19.8232 4.76256 19.8232 5.23744 19.5303 5.53033L13.061 12L19.5303 18.4697C19.7966 18.7359 19.8208 19.1526 19.6029 19.4462L19.5303 19.5303C19.2641 19.7966 18.8474 19.8208 18.5538 19.6029L18.4697 19.5303L12 13.061L5.53033 19.5303C5.23744 19.8232 4.76256 19.8232 4.46967 19.5303C4.17678 19.2374 4.17678 18.7626 4.46967 18.4697L10.939 12L4.46967 5.53033C4.2034 5.26406 4.1792 4.8474 4.39705 4.55379L4.46967 4.46967L4.39705 4.55379Z"
            fill="#EF4444"
        />
    </svg>
    <svg
        v-else-if="name == 'delete'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M11.5 4C11.5 3.17157 10.8284 2.5 10 2.5C9.17157 2.5 8.5 3.17157 8.5 4H7.5C7.5 2.61929 8.61929 1.5 10 1.5C11.3807 1.5 12.5 2.61929 12.5 4H17C17.2761 4 17.5 4.22386 17.5 4.5C17.5 4.77614 17.2761 5 17 5H16.446L15.1499 16.2292C15.0335 17.2384 14.179 18 13.1631 18H6.83688C5.821 18 4.9665 17.2384 4.85006 16.2292L3.553 5H3C2.75454 5 2.55039 4.82312 2.50806 4.58988L2.5 4.5C2.5 4.22386 2.72386 4 3 4H11.5ZM15.438 5H4.561L5.84347 16.1146C5.90169 16.6192 6.32894 17 6.83688 17H13.1631C13.6711 17 14.0983 16.6192 14.1565 16.1146L15.438 5ZM8.5 7.5C8.74546 7.5 8.94961 7.65477 8.99194 7.85886L9 7.9375V14.0625C9 14.3041 8.77614 14.5 8.5 14.5C8.25454 14.5 8.05039 14.3452 8.00806 14.1411L8 14.0625V7.9375C8 7.69588 8.22386 7.5 8.5 7.5ZM11.5 7.5C11.7455 7.5 11.9496 7.65477 11.9919 7.85886L12 7.9375V14.0625C12 14.3041 11.7761 14.5 11.5 14.5C11.2545 14.5 11.0504 14.3452 11.0081 14.1411L11 14.0625V7.9375C11 7.69588 11.2239 7.5 11.5 7.5Z"
            fill="#F87171"
        />
    </svg>
    <svg
        v-else-if="name == 'delete-shade'"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M6.5 7L6.5 11C6.5 11.2761 6.72386 11.5 7 11.5C7.27614 11.5 7.5 11.2761 7.5 11L7.5 7C7.5 6.72386 7.27614 6.5 7 6.5C6.72386 6.5 6.5 6.72386 6.5 7ZM9 6.5C9.27614 6.5 9.5 6.72386 9.5 7V11C9.5 11.2761 9.27614 11.5 9 11.5C8.72386 11.5 8.5 11.2761 8.5 11V7C8.5 6.72386 8.72386 6.5 9 6.5ZM10 4H13C13.2761 4 13.5 4.22386 13.5 4.5C13.5 4.77614 13.2761 5 13 5H12.4475L11.6946 11.7761C11.5539 13.0422 10.4838 14 9.20991 14H6.79008C5.51621 14 4.44605 13.0422 4.30537 11.7761L3.55247 5H3C2.72386 5 2.5 4.77614 2.5 4.5C2.5 4.22386 2.72386 4 3 4H6C6 2.89543 6.89543 2 8 2C9.10457 2 10 2.89543 10 4ZM8 3C7.44772 3 7 3.44772 7 4H9C9 3.44772 8.55229 3 8 3ZM4.55863 5L5.29925 11.6656C5.38366 12.4253 6.02575 13 6.79008 13H9.20991C9.97423 13 10.6163 12.4253 10.7007 11.6656L11.4414 5H4.55863Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'structure'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M2 4C2 2.89543 2.89543 2 4 2H9C10.1046 2 11 2.89543 11 4C11 5.10457 10.1046 6 9 6H4C2.89543 6 2 5.10457 2 4ZM4 3C3.44772 3 3 3.44772 3 4C3 4.55228 3.44772 5 4 5H9C9.55228 5 10 4.55228 10 4C10 3.44772 9.55228 3 9 3H4ZM2 10C2 8.89543 2.89543 8 4 8H12C13.1046 8 14 8.89543 14 10C14 11.1046 13.1046 12 12 12H4C2.89543 12 2 11.1046 2 10ZM4 9C3.44772 9 3 9.44772 3 10C3 10.5523 3.44772 11 4 11H12C12.5523 11 13 10.5523 13 10C13 9.44772 12.5523 9 12 9H4ZM2 16C2 14.8954 2.89543 14 4 14H16C17.1046 14 18 14.8954 18 16C18 17.1046 17.1046 18 16 18H4C2.89543 18 2 17.1046 2 16ZM4 15C3.44772 15 3 15.4477 3 16C3 16.5523 3.44772 17 4 17H16C16.5523 17 17 16.5523 17 16C17 15.4477 16.5523 15 16 15H4Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'template'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="'none'"
        :xmlns="xmlns"
    >
        <path
            d="M2.66667 3.33366C2.66667 2.96547 2.96514 2.66699 3.33333 2.66699H12.6667C13.0349 2.66699 13.3333 2.96547 13.3333 3.33366V4.66699C13.3333 5.03518 13.0349 5.33366 12.6667 5.33366H3.33333C2.96514 5.33366 2.66667 5.03518 2.66667 4.66699V3.33366Z"
            stroke="#9CA3AF"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
        <path
            d="M2.66667 8.66699C2.66667 8.2988 2.96514 8.00033 3.33333 8.00033H7.33333C7.70152 8.00033 8 8.2988 8 8.66699V12.667C8 13.0352 7.70152 13.3337 7.33333 13.3337H3.33333C2.96514 13.3337 2.66667 13.0352 2.66667 12.667V8.66699Z"
            stroke="#9CA3AF"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
        <path
            d="M10.6667 8.66699C10.6667 8.2988 10.9651 8.00033 11.3333 8.00033H12.6667C13.0349 8.00033 13.3333 8.2988 13.3333 8.66699V12.667C13.3333 13.0352 13.0349 13.3337 12.6667 13.3337H11.3333C10.9651 13.3337 10.6667 13.0352 10.6667 12.667V8.66699Z"
            stroke="#9CA3AF"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'document'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M7 12.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Zm.75 2.25a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5ZM7 18.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Zm3.75-6.75a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5ZM10 15.25a.75.75 0 0 1 .75-.75h5.5a.75.75 0 0 1 0 1.5h-5.5a.75.75 0 0 1-.75-.75Zm.75 2.25a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5Zm8.664-9.086-5.829-5.828a.493.493 0 0 0-.049-.04.626.626 0 0 1-.036-.03 2.072 2.072 0 0 0-.219-.18.652.652 0 0 0-.08-.044l-.048-.024-.05-.029c-.054-.031-.109-.063-.166-.087a1.977 1.977 0 0 0-.624-.138c-.02-.001-.04-.004-.059-.007A.605.605 0 0 0 12.172 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9.828a2 2 0 0 0-.586-1.414ZM18.5 20a.5.5 0 0 1-.5.5H6a.5.5 0 0 1-.5-.5V4a.5.5 0 0 1 .5-.5h6V8a2 2 0 0 0 2 2h4.5v10Zm-5-15.379L17.378 8.5H14a.5.5 0 0 1-.5-.5V4.621Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'icon-pdf'"
        :xmlns="xmlns"
        :width="width"
        :height="height"
        viewBox="0 0 14 16"
        :fill="bgfill"
    >
        <path
            opacity="0.64"
            d="M12.1867 3.28467L9.04867 0.146333C8.95473 0.0529684 8.82778 0.00039111 8.69534 0H2.16667C1.89101 0 1.66667 0.224333 1.66667 0.5V13.5C1.66667 13.7757 1.89101 14 2.16667 14H11.8333C12.109 14 12.3333 13.7757 12.3333 13.5V3.638C12.3333 3.50467 12.2813 3.379 12.1867 3.28467ZM9.00001 0.569L11.7643 3.33333H9.16667C9.1225 3.33325 9.08016 3.31566 9.04892 3.28442C9.01768 3.25318 9.00009 3.21084 9.00001 3.16667V0.569ZM11.8333 13.6667H2.16667C2.1225 13.6666 2.08015 13.649 2.04892 13.6178C2.01768 13.5865 2.00009 13.5442 2.00001 13.5V0.5C2.00001 0.408333 2.07501 0.333333 2.16667 0.333333H8.66667V3.16667C8.66667 3.44233 8.89101 3.66667 9.16667 3.66667H12V13.5C12 13.5917 11.925 13.6667 11.8333 13.6667Z"
            fill="#605E5C"
        />
        <path
            d="M6.50001 1H2.66667V1.2H6.50001C6.54421 1.2 6.5866 1.18946 6.61786 1.17071C6.64911 1.15196 6.66667 1.12652 6.66667 1.1C6.66667 1.07348 6.64911 1.04804 6.61786 1.02929C6.5866 1.01054 6.54421 1 6.50001 1Z"
            fill="#959494"
        />
        <path
            d="M6.50001 2H2.66667V2.2H6.50001C6.54421 2.2 6.5866 2.18946 6.61786 2.17071C6.64911 2.15196 6.66667 2.12652 6.66667 2.1C6.66667 2.07348 6.64911 2.04804 6.61786 2.02929C6.5866 2.01054 6.54421 2 6.50001 2Z"
            fill="#959494"
        />
        <path
            d="M10.3333 4H2.66667V4.2H10.3333C10.4217 4.2 10.5065 4.18946 10.569 4.17071C10.6316 4.15196 10.6667 4.12652 10.6667 4.1C10.6667 4.07348 10.6316 4.04804 10.569 4.02929C10.5065 4.01054 10.4217 4 10.3333 4Z"
            fill="#959494"
        />
        <path
            d="M10.3333 5H2.66667V5.2H10.3333C10.4217 5.2 10.5065 5.18946 10.569 5.17071C10.6316 5.15196 10.6667 5.12652 10.6667 5.1C10.6667 5.07348 10.6316 5.04804 10.569 5.02929C10.5065 5.01054 10.4217 5 10.3333 5Z"
            fill="#959494"
        />
        <path
            d="M10.3333 6H2.66667V6.2H10.3333C10.4217 6.2 10.5065 6.18946 10.569 6.17071C10.6316 6.15196 10.6667 6.12652 10.6667 6.1C10.6667 6.07348 10.6316 6.04804 10.569 6.02929C10.5065 6.01054 10.4217 6 10.3333 6Z"
            fill="#959494"
        />
        <path
            d="M10.3333 12.5H2.66667V12.7H10.3333C10.4217 12.7 10.5065 12.6895 10.569 12.6707C10.6316 12.652 10.6667 12.6265 10.6667 12.6C10.6667 12.5735 10.6316 12.548 10.569 12.5293C10.5065 12.5105 10.4217 12.5 10.3333 12.5Z"
            fill="#959494"
        />
        <rect x="0.666672" y="7" width="12" height="5" fill="#EB1E2E" />
        <path
            d="M3.33334 8.47339C3.33334 8.34139 3.43734 8.19739 3.60484 8.19739H4.52834C5.04834 8.19739 5.51634 8.54539 5.51634 9.21239C5.51634 9.84439 5.04834 10.1964 4.52834 10.1964H3.86084V10.7244C3.86084 10.9004 3.74884 10.9999 3.60484 10.9999C3.47284 10.9999 3.33334 10.9004 3.33334 10.7244V8.47339ZM3.86084 8.70089V9.69689H4.52834C4.79634 9.69689 5.00834 9.46039 5.00834 9.21239C5.00834 8.93289 4.79634 8.70089 4.52834 8.70089H3.86084Z"
            fill="white"
        />
        <path
            d="M6.29933 10.9999C6.16733 10.9999 6.02333 10.9279 6.02333 10.7524V8.4814C6.02333 8.3379 6.16733 8.2334 6.29933 8.2334H7.21483C9.04183 8.2334 9.00183 10.9999 7.25083 10.9999H6.29933ZM6.55133 8.7214V10.5124H7.21483C8.29433 10.5124 8.34233 8.7214 7.21483 8.7214H6.55133Z"
            fill="white"
        />
        <path
            d="M9.64986 8.75341V9.38891H10.6694C10.8134 9.38891 10.9574 9.53291 10.9574 9.67241C10.9574 9.80441 10.8134 9.91241 10.6694 9.91241H9.64986V10.7519C9.64986 10.8919 9.55036 10.9994 9.41036 10.9994C9.23436 10.9994 9.12686 10.8919 9.12686 10.7519V8.48091C9.12686 8.33741 9.23486 8.23291 9.41036 8.23291H10.8139C10.9899 8.23291 11.0939 8.33741 11.0939 8.48091C11.0939 8.60891 10.9899 8.75291 10.8139 8.75291H9.64986V8.75341Z"
            fill="white"
        />
    </svg>
    <svg
        v-else-if="name == 'icon-xlsx'"
        :xmlns="xmlns"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
    >
        <path
            d="M4.5 14.6667H14.1667C14.2583 14.6667 14.3333 14.5917 14.3333 14.5V4.66671H11.5C11.2243 4.66671 11 4.44237 11 4.16671V1.33337H4.5C4.40834 1.33337 4.33334 1.40837 4.33334 1.50004V14.5C4.33334 14.5917 4.40834 14.6667 4.5 14.6667Z"
            fill="white"
        />
        <path
            d="M14.0977 4.3333L11.3333 1.56897V4.16664C11.3333 4.2583 11.4083 4.3333 11.5 4.3333H14.0977Z"
            fill="white"
        />
        <path
            opacity="0.64"
            d="M14.52 4.28467L11.382 1.14633C11.2881 1.05297 11.1611 1.00039 11.0287 1H4.5C4.22433 1 4 1.22433 4 1.5V14.5C4 14.7757 4.22433 15 4.5 15H14.1667C14.4423 15 14.6667 14.7757 14.6667 14.5V4.638C14.6667 4.50467 14.6147 4.379 14.52 4.28467ZM11.3333 1.569L14.0977 4.33333H11.5C11.4558 4.33325 11.4135 4.31566 11.3822 4.28442C11.351 4.25318 11.3334 4.21084 11.3333 4.16667V1.569ZM14.1667 14.6667H4.5C4.45582 14.6666 4.41348 14.649 4.38225 14.6178C4.35101 14.5865 4.33342 14.5442 4.33333 14.5V1.5C4.33333 1.40833 4.40833 1.33333 4.5 1.33333H11V4.16667C11 4.44233 11.2243 4.66667 11.5 4.66667H14.3333V14.5C14.3333 14.5917 14.2583 14.6667 14.1667 14.6667Z"
            fill="#605E5C"
        />
        <path
            d="M13 10.3333H12C11.9116 10.3333 11.8268 10.2982 11.7643 10.2357C11.7018 10.1731 11.6667 10.0884 11.6667 9.99996C11.6667 9.91155 11.7018 9.82677 11.7643 9.76426C11.8268 9.70174 11.9116 9.66663 12 9.66663H13C13.0884 9.66663 13.1732 9.70174 13.2357 9.76426C13.2982 9.82677 13.3333 9.91155 13.3333 9.99996C13.3333 10.0884 13.2982 10.1731 13.2357 10.2357C13.1732 10.2982 13.0884 10.3333 13 10.3333Z"
            fill="#134A2C"
        />
        <path
            d="M10.6667 10.3333H9.66666C9.57826 10.3333 9.49347 10.2982 9.43096 10.2357C9.36845 10.1731 9.33333 10.0884 9.33333 9.99996C9.33333 9.91155 9.36845 9.82677 9.43096 9.76426C9.49347 9.70174 9.57826 9.66663 9.66666 9.66663H10.6667C10.7551 9.66663 10.8399 9.70174 10.9024 9.76426C10.9649 9.82677 11 9.91155 11 9.99996C11 10.0884 10.9649 10.1731 10.9024 10.2357C10.8399 10.2982 10.7551 10.3333 10.6667 10.3333Z"
            fill="#185C37"
        />
        <path
            d="M13 9.00004H12C11.9116 9.00004 11.8268 8.96492 11.7643 8.90241C11.7018 8.8399 11.6667 8.75511 11.6667 8.66671C11.6667 8.5783 11.7018 8.49352 11.7643 8.431C11.8268 8.36849 11.9116 8.33337 12 8.33337H13C13.0884 8.33337 13.1732 8.36849 13.2357 8.431C13.2982 8.49352 13.3333 8.5783 13.3333 8.66671C13.3333 8.75511 13.2982 8.8399 13.2357 8.90241C13.1732 8.96492 13.0884 9.00004 13 9.00004Z"
            fill="#21A366"
        />
        <path
            d="M10.6667 9.00004H9.66666C9.57826 9.00004 9.49347 8.96492 9.43096 8.90241C9.36845 8.8399 9.33333 8.75511 9.33333 8.66671C9.33333 8.5783 9.36845 8.49352 9.43096 8.431C9.49347 8.36849 9.57826 8.33337 9.66666 8.33337H10.6667C10.7551 8.33337 10.8399 8.36849 10.9024 8.431C10.9649 8.49352 11 8.5783 11 8.66671C11 8.75511 10.9649 8.8399 10.9024 8.90241C10.8399 8.96492 10.7551 9.00004 10.6667 9.00004Z"
            fill="#107C41"
        />
        <path
            d="M13 7.66667H12C11.9116 7.66667 11.8268 7.63155 11.7643 7.56904C11.7018 7.50652 11.6667 7.42174 11.6667 7.33333C11.6667 7.24493 11.7018 7.16014 11.7643 7.09763C11.8268 7.03512 11.9116 7 12 7H13C13.0884 7 13.1732 7.03512 13.2357 7.09763C13.2982 7.16014 13.3333 7.24493 13.3333 7.33333C13.3333 7.42174 13.2982 7.50652 13.2357 7.56904C13.1732 7.63155 13.0884 7.66667 13 7.66667Z"
            fill="#33C481"
        />
        <path
            d="M10.6667 7.66667H9.66666C9.57826 7.66667 9.49347 7.63155 9.43096 7.56904C9.36845 7.50652 9.33333 7.42174 9.33333 7.33333C9.33333 7.24493 9.36845 7.16014 9.43096 7.09763C9.49347 7.03512 9.57826 7 9.66666 7H10.6667C10.7551 7 10.8399 7.03512 10.9024 7.09763C10.9649 7.16014 11 7.24493 11 7.33333C11 7.42174 10.9649 7.50652 10.9024 7.56904C10.8399 7.63155 10.7551 7.66667 10.6667 7.66667Z"
            fill="#21A366"
        />
        <path
            d="M2 12.3333H8C8.17681 12.3333 8.34638 12.2631 8.47141 12.1381C8.59643 12.013 8.66667 11.8435 8.66667 11.6667V5.66667C8.66667 5.48986 8.59643 5.32029 8.47141 5.19526C8.34638 5.07024 8.17681 5 8 5H2C1.82319 5 1.65362 5.07024 1.5286 5.19526C1.40357 5.32029 1.33334 5.48986 1.33334 5.66667V11.6667C1.33334 11.8435 1.40357 12.013 1.5286 12.1381C1.65362 12.2631 1.82319 12.3333 2 12.3333Z"
            fill="#107C41"
        />
        <path
            d="M3.17267 10.6666L4.45633 8.66096L3.28033 6.66663H4.22667L4.86867 7.94163C4.92767 8.06229 4.968 8.15263 4.99033 8.21196H4.99867C5.041 8.11529 5.08533 8.02129 5.13133 7.93029L5.818 6.66663H6.68633L5.47967 8.64996L6.71667 10.6666H5.79267L5.051 9.26663C5.01611 9.20701 4.98655 9.14444 4.96267 9.07963H4.95167C4.93019 9.14306 4.90144 9.2038 4.866 9.26063L4.10233 10.6666H3.17233H3.17267Z"
            fill="#F9F7F7"
        />
    </svg>
    <svg
        v-else-if="name == 'flag'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="'none'"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M4.5 13H15.5C15.8993 13 16.1375 12.5549 15.916 12.2226L13.1009 8L15.916 3.77735C16.1375 3.44507 15.8993 3 15.5 3H4C3.72386 3 3.5 3.22386 3.5 3.5V17.5C3.5 17.7761 3.72386 18 4 18C4.27614 18 4.5 17.7761 4.5 17.5V13ZM4.5 12V4H14.5657L12.084 7.72265C11.972 7.8906 11.972 8.1094 12.084 8.27735L14.5657 12H4.5Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'loading-spinner'"
        :xmlns="xmlns"
        class="animate-spin"
        :width="width"
        :height="height"
        viewBox="0 0 50 50"
        :fill="'none'"
        v-bind="$attrs"
    >
        <circle cx="25" cy="25" r="22.5" :stroke="fill" stroke-width="5" />
        <mask id="path-2-inside-1_5811_136532" fill="white">
            <path
                d="M41.5841 40.2108C42.6002 41.1429 44.191 41.0813 45.0171 39.9773C48.1972 35.7273 49.9556 30.5547 49.9992 25.2037C50.0428 19.8528 48.3689 14.6522 45.2586 10.351C44.4506 9.23366 42.861 9.14615 41.8298 10.0615C40.7986 10.9768 40.7195 12.5474 41.4994 13.6846C43.8042 17.0452 45.0396 21.0485 45.006 25.163C44.9725 29.2776 43.6721 33.2603 41.3128 36.5829C40.5145 37.7071 40.5679 39.2788 41.5841 40.2108Z"
            />
        </mask>
        <path
            d="M41.5841 40.2108C42.6002 41.1429 44.191 41.0813 45.0171 39.9773C48.1972 35.7273 49.9556 30.5547 49.9992 25.2037C50.0428 19.8528 48.3689 14.6522 45.2586 10.351C44.4506 9.23366 42.861 9.14615 41.8298 10.0615C40.7986 10.9768 40.7195 12.5474 41.4994 13.6846C43.8042 17.0452 45.0396 21.0485 45.006 25.163C44.9725 29.2776 43.6721 33.2603 41.3128 36.5829C40.5145 37.7071 40.5679 39.2788 41.5841 40.2108Z"
            :stroke="stroke"
            stroke-width="10"
            stroke-linejoin="round"
            mask="url(#path-2-inside-1_5811_136532)"
        />
    </svg>
    <svg
        v-else-if="name == 'loading'"
        :width="width"
        :height="height"
        :xmlns="xmlns"
        class="animate-spin"
        :class="className"
        viewBox="0 0 50 50"
        :fill="bgfill"
    >
        <circle cx="25" cy="25" r="22.5" :stroke="stroke" stroke-width="5" />
        <mask id="path-2-inside-1_5811_136532" fill="white">
            <path
                d="M41.5841 40.2108C42.6002 41.1429 44.191 41.0813 45.0171 39.9773C48.1972 35.7273 49.9556 30.5547 49.9992 25.2037C50.0428 19.8528 48.3689 14.6522 45.2586 10.351C44.4506 9.23366 42.861 9.14615 41.8298 10.0615C40.7986 10.9768 40.7195 12.5474 41.4994 13.6846C43.8042 17.0452 45.0396 21.0485 45.006 25.163C44.9725 29.2776 43.6721 33.2603 41.3128 36.5829C40.5145 37.7071 40.5679 39.2788 41.5841 40.2108Z"
            />
        </mask>
        <path
            d="M41.5841 40.2108C42.6002 41.1429 44.191 41.0813 45.0171 39.9773C48.1972 35.7273 49.9556 30.5547 49.9992 25.2037C50.0428 19.8528 48.3689 14.6522 45.2586 10.351C44.4506 9.23366 42.861 9.14615 41.8298 10.0615C40.7986 10.9768 40.7195 12.5474 41.4994 13.6846C43.8042 17.0452 45.0396 21.0485 45.006 25.163C44.9725 29.2776 43.6721 33.2603 41.3128 36.5829C40.5145 37.7071 40.5679 39.2788 41.5841 40.2108Z"
            :stroke="fill"
            stroke-width="10"
            stroke-linejoin="round"
            mask="url(#path-2-inside-1_5811_136532)"
        />
    </svg>
    <svg
        v-else-if="name == 'chevron-up'"
        :width="width"
        :height="height"
        viewBox="0 0 12 12"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M0.1464 4.3536 C 0.3417 4.5488 0.6583 4.5488 0.8536 4.3536 L 4 1.2071 L 7.1464 4.3536 C 7.3417 4.5488 7.6583 4.5488 7.8536 4.3536 C 8.0488 4.1583 8.0488 3.8417 7.8536 3.6465 L 4.3536 0.1464 C 4.1583 -0.0488 3.8417 -0.0488 3.6465 0.1464 L 0.1464 3.6465 C -0.0488 3.8417 -0.0488 4.1583 0.1464 4.3536Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'chevron-down'"
        :width="width"
        :height="height"
        viewBox="0 0 12 12"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M2.21967 4.46967C2.51256 4.17678 2.98744 4.17678 3.28033 4.46967L6 7.18934L8.71967 4.46967C9.01256 4.17678 9.48744 4.17678 9.78033 4.46967C10.0732 4.76256 10.0732 5.23744 9.78033 5.53033L6.53033 8.78033C6.23744 9.07322 5.76256 9.07322 5.46967 8.78033L2.21967 5.53033C1.92678 5.23744 1.92678 4.76256 2.21967 4.46967Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'chevron-left'"
        :width="width"
        :height="height"
        viewBox="0 0 21 20"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M13.373 15.8337L7.53971 10.0003L13.373 4.16699"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'chevron-right'"
        :width="width"
        :height="height"
        viewBox="0 0 21 20"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M8.37305 4.16634L14.2064 9.99967L8.37305 15.833"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'chevron-up-down'"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M4.14645 6.35355C4.34171 6.54882 4.65829 6.54882 4.85355 6.35355L8 3.20711L11.1464 6.35355C11.3417 6.54882 11.6583 6.54882 11.8536 6.35355C12.0488 6.15829 12.0488 5.84171 11.8536 5.64645L8.35355 2.14645C8.15829 1.95118 7.84171 1.95118 7.64645 2.14645L4.14645 5.64645C3.95118 5.84171 3.95118 6.15829 4.14645 6.35355ZM4.14645 9.64645C4.34171 9.45118 4.65829 9.45118 4.85355 9.64645L8 12.7929L11.1464 9.64645C11.3417 9.45118 11.6583 9.45118 11.8536 9.64645C12.0488 9.84171 12.0488 10.1583 11.8536 10.3536L8.35355 13.8536C8.15829 14.0488 7.84171 14.0488 7.64645 13.8536L4.14645 10.3536C3.95118 10.1583 3.95118 9.84171 4.14645 9.64645Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'toggle-off'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path d="M7.25 14.5a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5Z" :fill="fill" />
        <path
            d="M22 12a5 5 0 0 0-5-5H7a5 5 0 0 0 0 10h10a5 5 0 0 0 5-5Zm-5-3.5a3.5 3.5 0 1 1 0 7H7a3.5 3.5 0 1 1 0-7h10Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'upload'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 8L12 4M12 4L8 8M12 4L12 16"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'play'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M8 5.14V19.14C8 19.94 8.85 20.46 9.53 20.08L20.53 13.08C21.23 12.69 21.23 11.69 20.53 11.29L9.53 4.29C8.85 3.91 8 4.43 8 5.14Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'pause'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path d="M6 19H10V5H6V19ZM14 5V19H18V5H14Z" :fill="fill" />
    </svg>
    <svg
        v-else-if="name == 'markattendance'"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <g clip-path="url(#clip0_3986_2106)">
            <path
                d="M12.5 8C14.9853 8 17 5.98528 17 3.5C17 1.01472 14.9853 -1 12.5 -1C10.0147 -1 8 1.01472 8 3.5C8 5.98528 10.0147 8 12.5 8ZM14.8536 2.35355L11.8536 5.35355C11.6583 5.54882 11.3417 5.54882 11.1464 5.35355L10.1464 4.35355C9.95118 4.15829 9.95118 3.84171 10.1464 3.64645C10.3417 3.45118 10.6583 3.45118 10.8536 3.64645L11.5 4.29289L14.1464 1.64645C14.3417 1.45118 14.6583 1.45118 14.8536 1.64645C15.0488 1.84171 15.0488 2.15829 14.8536 2.35355Z"
                :fill="fill"
            />
            <path
                d="M12.8451 8.98935C12.731 8.99642 12.6159 9 12.5 9C12.249 9 12.0018 8.98318 11.7597 8.95061L8.5 14.5113V8.86622C8.7989 8.69331 9 8.37014 9 8C9 7.89778 8.98466 7.79913 8.95616 7.70625C8.85918 7.3902 8.6098 7.14082 8.29375 7.04384C8.20087 7.01534 8.10222 7 8 7C7.44772 7 7 7.44772 7 8C7 8.37014 7.2011 8.69331 7.5 8.86622V14.5113L4.21603 8.90925C3.96513 8.48126 3.94209 7.9569 4.15447 7.50854L5.81641 4H7.02242C7.00758 3.83532 7 3.66854 7 3.5C7 3.33146 7.00758 3.16468 7.02242 3H4.5C4.22386 3 4 2.77614 4 2.5V0.5C4 0.223858 3.77614 0 3.5 0C3.22386 0 3 0.223858 3 0.5V2.5C3 3.32843 3.67157 4 4.5 4H4.70989L3.25073 7.08046C2.89676 7.82771 2.93517 8.70165 3.35333 9.41497L6.81578 15.3215C7.06215 15.7418 7.51282 16 7.99999 16C8.48716 16 8.93783 15.7418 9.1842 15.3215L12.6467 9.41497C12.7268 9.27832 12.7929 9.13577 12.8451 8.98935Z"
                :fill="fill"
            />
        </g>
        <defs>
            <clipPath id="clip0_3986_2106">
                <rect width="16" height="16" :fill="fill" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'trainer'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M12.5 2.75a.75.75 0 0 0-1.5 0V3H5.25A3.25 3.25 0 0 0 2 6.25v9.5A3.25 3.25 0 0 0 5.25 19h2.398l-1.475 1.77a.75.75 0 0 0 1.153.96L9.6 19h2.428a4.285 4.285 0 0 1-.029-.5V18c0-.171.017-.338.05-.5h-6.8a1.75 1.75 0 0 1-1.75-1.75v-9.5c0-.966.784-1.75 1.75-1.75h13.5c.966 0 1.75.784 1.75 1.75v3.705A3.74 3.74 0 0 1 21.742 13H22V6.25A3.25 3.25 0 0 0 18.75 3H12.5v-.25ZM20.739 13a2.74 2.74 0 0 0-.239-1.397A2.75 2.75 0 1 0 20.739 13Zm.761 3.5A1.5 1.5 0 0 1 23 18v.5c0 1.971-1.86 4-5 4-2.875 0-4.676-1.7-4.96-3.5a3.21 3.21 0 0 1-.04-.5V18a1.497 1.497 0 0 1 1.5-1.5h7ZM6 7.75A.75.75 0 0 1 6.75 7h4a.75.75 0 0 1 0 1.5h-4A.75.75 0 0 1 6 7.75ZM6.75 10a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5ZM6 13.75a.75.75 0 0 1 .75-.75h5.5a.75.75 0 0 1 0 1.5h-5.5a.75.75 0 0 1-.75-.75Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'printer'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M15.752 3a2.25 2.25 0 0 1 2.25 2.25v.753h.75a3.254 3.254 0 0 1 3.252 3.25l.003 5.997a2.249 2.249 0 0 1-2.248 2.25H18v1.25A2.25 2.25 0 0 1 15.75 21h-7.5A2.25 2.25 0 0 1 6 18.75V17.5H4.25A2.25 2.25 0 0 1 2 15.25V9.254a3.25 3.25 0 0 1 3.25-3.25l.749-.001L6 5.25A2.25 2.25 0 0 1 8.25 3h7.502Zm-.002 10.5h-7.5a.75.75 0 0 0-.75.75v4.5c0 .414.336.75.75.75h7.5a.75.75 0 0 0 .75-.75v-4.5a.75.75 0 0 0-.75-.75Zm3.002-5.996H5.25a1.75 1.75 0 0 0-1.75 1.75v5.996c0 .414.336.75.75.75H6v-1.75A2.25 2.25 0 0 1 8.25 12h7.5A2.25 2.25 0 0 1 18 14.25V16h1.783a.749.749 0 0 0 .724-.749l-.003-5.997a1.754 1.754 0 0 0-1.752-1.75Zm-3-3.004H8.25a.75.75 0 0 0-.75.75l-.001.753h9.003V5.25a.75.75 0 0 0-.75-.75Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'download_arrow'"
        :width="width"
        :height="height"
        viewBox="0 0 10 13"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M0.5 12H9.5C9.77614 12 10 12.2239 10 12.5C10 12.7455 9.82312 12.9496 9.58988 12.9919L9.5 13H0.5C0.223858 13 0 12.7761 0 12.5C0 12.2545 0.176875 12.0504 0.410124 12.0081L0.5 12H9.5H0.5ZM4.91012 0.00805569L5 0C5.24546 0 5.44961 0.176875 5.49194 0.410124L5.5 0.5V9.292L8.18198 6.61091C8.35555 6.43735 8.62497 6.41806 8.81984 6.55306L8.88909 6.61091C9.06265 6.78448 9.08194 7.0539 8.94694 7.24877L8.88909 7.31802L5.35355 10.8536C5.17999 11.0271 4.91056 11.0464 4.71569 10.9114L4.64645 10.8536L1.11091 7.31802C0.915651 7.12276 0.915651 6.80617 1.11091 6.61091C1.28448 6.43735 1.5539 6.41806 1.74877 6.55306L1.81802 6.61091L4.5 9.292V0.5C4.5 0.25454 4.67688 0.0503916 4.91012 0.00805569L5 0L4.91012 0.00805569Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'column-filter'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M12 19.137A2.742 2.742 0 0 1 10 20H3.75A1.75 1.75 0 0 1 2 18.25V5.75C2 4.784 2.784 4 3.75 4H10c.788 0 1.499.331 2 .863A2.742 2.742 0 0 1 14 4h6.25c.966 0 1.75.784 1.75 1.75v12.5A1.75 1.75 0 0 1 20.25 20H14a2.742 2.742 0 0 1-2-.863ZM3.5 5.75v12.5c0 .138.112.25.25.25H10c.69 0 1.25-.56 1.25-1.25V6.75c0-.69-.56-1.25-1.25-1.25H3.75a.25.25 0 0 0-.25.25Zm9.25 11.5c0 .69.56 1.25 1.25 1.25h6.25a.25.25 0 0 0 .25-.25V5.75a.25.25 0 0 0-.25-.25H14c-.69 0-1.25.56-1.25 1.25v10.5Z"
            :stroke="fill"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'gear' || name == 'setting'"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M7.99994 6C6.89537 6 5.99994 6.89543 5.99994 8C5.99994 9.10457 6.89537 10 7.99994 10C9.10451 10 9.99994 9.10457 9.99994 8C9.99994 6.89543 9.10451 6 7.99994 6ZM6.99994 8C6.99994 7.44772 7.44765 7 7.99994 7C8.55222 7 8.99994 7.44772 8.99994 8C8.99994 8.55228 8.55222 9 7.99994 9C7.44765 9 6.99994 8.55228 6.99994 8ZM10.618 4.39833C10.233 4.46825 9.86392 4.21413 9.7937 3.83074L9.53397 2.41496C9.50816 2.27427 9.39961 2.16301 9.25912 2.13325C8.84818 2.04621 8.42685 2.00195 8 2.00195C7.57289 2.00195 7.1513 2.04627 6.74013 2.13341C6.5996 2.1632 6.49104 2.27452 6.46529 2.41527L6.20629 3.8308C6.1994 3.86844 6.18942 3.90551 6.17647 3.9416C6.04476 4.30859 5.6392 4.49978 5.27062 4.36863L3.91115 3.88463C3.77603 3.83652 3.62511 3.87431 3.52891 3.98033C2.96005 4.60729 2.52892 5.34708 2.2672 6.15302C2.22305 6.28899 2.26562 6.43805 2.37502 6.53053L3.47694 7.46206C3.50626 7.48685 3.53352 7.51399 3.55843 7.5432C3.81177 7.84027 3.77528 8.28558 3.47693 8.53783L2.37502 9.46935C2.26562 9.56183 2.22305 9.71089 2.2672 9.84685C2.52892 10.6528 2.96005 11.3926 3.52891 12.0196C3.62511 12.1256 3.77603 12.1634 3.91115 12.1153L5.27068 11.6312C5.30687 11.6184 5.3441 11.6084 5.38196 11.6015C5.76701 11.5316 6.13608 11.7857 6.2063 12.1691L6.46529 13.5846C6.49104 13.7254 6.5996 13.8367 6.74013 13.8665C7.1513 13.9536 7.57289 13.9979 8 13.9979C8.42685 13.9979 8.84818 13.9537 9.25912 13.8666C9.39961 13.8369 9.50816 13.7256 9.53397 13.5849L9.79368 12.1692C9.8006 12.1314 9.81058 12.0944 9.82353 12.0583C9.95524 11.6913 10.3608 11.5001 10.7294 11.6312L12.0888 12.1153C12.224 12.1634 12.3749 12.1256 12.4711 12.0196C13.04 11.3926 13.4711 10.6528 13.7328 9.84685C13.777 9.71089 13.7344 9.56183 13.625 9.46935L12.5231 8.53782C12.4937 8.51303 12.4665 8.48589 12.4416 8.45667C12.1882 8.1596 12.2247 7.71429 12.5231 7.46205L13.625 6.53053C13.7344 6.43805 13.777 6.28899 13.7328 6.15302C13.4711 5.34708 13.04 4.60729 12.4711 3.98033C12.3749 3.87431 12.224 3.83652 12.0888 3.88463L10.7293 4.36865C10.6931 4.38152 10.6559 4.39146 10.618 4.39833ZM3.99863 4.97726L4.93522 5.3107C5.82017 5.62559 6.79872 5.16815 7.11769 4.2794C7.14903 4.19207 7.17324 4.1021 7.18996 4.01078L7.36738 3.04113C7.5757 3.01512 7.78684 3.00195 8 3.00195C8.213 3.00195 8.42397 3.0151 8.63214 3.04107L8.81011 4.01117C8.98053 4.9408 9.87266 5.55003 10.7967 5.38225C10.8877 5.36572 10.9775 5.34176 11.0647 5.31073L12.0014 4.97726C12.2564 5.31084 12.4684 5.67476 12.6319 6.06064L11.8774 6.6984C11.1566 7.30787 11.0675 8.38649 11.6807 9.10555C11.7408 9.17609 11.8067 9.24166 11.8775 9.3015L12.6319 9.93924C12.4684 10.3251 12.2564 10.689 12.0014 11.0226L11.0646 10.6891C10.1797 10.3742 9.20128 10.8317 8.88231 11.7205C8.85096 11.8078 8.82677 11.8978 8.81004 11.9891L8.63214 12.9588C8.42397 12.9848 8.213 12.9979 8 12.9979C7.78684 12.9979 7.5757 12.9848 7.36738 12.9587L7.18994 11.989C7.01965 11.0592 6.12743 10.4498 5.2033 10.6176C5.11227 10.6342 5.0225 10.6581 4.93528 10.6892L3.99863 11.0226C3.74357 10.689 3.53161 10.3251 3.36814 9.93924L4.12257 9.30148C4.84343 8.69201 4.93254 7.61339 4.31933 6.89433C4.25917 6.82378 4.19332 6.75822 4.12254 6.69838L3.36814 6.06064C3.53161 5.67476 3.74357 5.31084 3.99863 4.97726Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'copy'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M8 2C6.89543 2 6 2.89543 6 4V14C6 15.1046 6.89543 16 8 16H14C15.1046 16 16 15.1046 16 14V4C16 2.89543 15.1046 2 14 2H8ZM7 4C7 3.44772 7.44772 3 8 3H14C14.5523 3 15 3.44772 15 4V14C15 14.5523 14.5523 15 14 15H8C7.44772 15 7 14.5523 7 14V4ZM4 6.00001C4 5.25973 4.4022 4.61339 5 4.26758V14.5C5 15.8807 6.11929 17 7.5 17H13.7324C13.3866 17.5978 12.7403 18 12 18H7.5C5.567 18 4 16.433 4 14.5V6.00001Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'book'"
        :width="width"
        :height="height"
        viewBox="0 0 30 36"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M5.4 7.2C5.4 6.20589 6.20589 5.4 7.2 5.4H21.6C22.5941 5.4 23.4 6.20589 23.4 7.2V10.8C23.4 11.7941 22.5941 12.6 21.6 12.6H7.2C6.20589 12.6 5.4 11.7941 5.4 10.8V7.2ZM8.1 9.9H20.7V8.1H8.1V9.9ZM0 4.5C0 2.01472 2.01472 0 4.5 0H25.2C27.6853 0 29.7 2.01472 29.7 4.5V30.15C29.7 30.8956 29.0956 31.5 28.35 31.5H2.7C2.7 32.4941 3.50589 33.3 4.5 33.3H28.35C29.0956 33.3 29.7 33.9044 29.7 34.65C29.7 35.3956 29.0956 36 28.35 36H4.5C2.01472 36 0 33.9853 0 31.5V4.5ZM2.7 28.8H27V4.5C27 3.50589 26.1941 2.7 25.2 2.7H4.5C3.50589 2.7 2.7 3.50589 2.7 4.5V28.8Z"
            fill="#9CA3AF"
        />
    </svg>
    <svg
        v-else-if="name == 'action-menu'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            :fill="fill"
            d="M8 12a2 2 0 1 1-4 0a2 2 0 0 1 4 0m6 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0m4 2a2 2 0 1 0 0-4a2 2 0 0 0 0 4"
        />
    </svg>
    <svg
        v-else-if="name == 'sync-time'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M12 3a9 9 0 1 1-8.963 8.182.75.75 0 1 1 1.494.135 7.46 7.46 0 0 0 2.166 5.986A7.46 7.46 0 0 0 12 19.5 7.5 7.5 0 1 0 5.997 7.502h2.756a.75.75 0 0 1 .102 1.493l-.102.007H4.25a.75.75 0 0 1-.743-.648L3.5 8.252v-4.5a.75.75 0 0 1 1.493-.102L5 3.752l-.001 2.591A8.986 8.986 0 0 1 12 3Zm-.75 4a.75.75 0 0 1 .743.648L12 7.75V12h2.25a.75.75 0 0 1 .102 1.493l-.102.007h-3a.75.75 0 0 1-.743-.648l-.007-.102v-5a.75.75 0 0 1 .75-.75Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'help'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2ZM10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3ZM10 13.5C10.4142 13.5 10.75 13.8358 10.75 14.25C10.75 14.6642 10.4142 15 10 15C9.58579 15 9.25 14.6642 9.25 14.25C9.25 13.8358 9.58579 13.5 10 13.5ZM10 5.5C11.3807 5.5 12.5 6.61929 12.5 8C12.5 8.72959 12.1848 9.40774 11.6513 9.8771L11.4967 10.0024L11.2782 10.1655L11.1906 10.2372C11.1348 10.2851 11.0835 10.3337 11.0346 10.3859C10.6963 10.7464 10.5 11.2422 10.5 12C10.5 12.2761 10.2761 12.5 10 12.5C9.72386 12.5 9.5 12.2761 9.5 12C9.5 10.988 9.79312 10.2475 10.3054 9.70162C10.4165 9.5832 10.532 9.47988 10.6609 9.37874L10.9076 9.19439L11.0256 9.09468C11.325 8.81435 11.5 8.42206 11.5 8C11.5 7.17157 10.8284 6.5 10 6.5C9.17157 6.5 8.5 7.17157 8.5 8C8.5 8.27614 8.27614 8.5 8 8.5C7.72386 8.5 7.5 8.27614 7.5 8C7.5 6.61929 8.61929 5.5 10 5.5Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'locked'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
    >
        <path
            d="M10 2C11.6569 2 13 3.34315 13 5V6H14C15.1046 6 16 6.89543 16 8V15C16 16.1046 15.1046 17 14 17H6C4.89543 17 4 16.1046 4 15V8C4 6.89543 4.89543 6 6 6H7V5C7 3.34315 8.34315 2 10 2ZM14 7H6C5.44772 7 5 7.44772 5 8V15C5 15.5523 5.44772 16 6 16H14C14.5523 16 15 15.5523 15 15V8C15 7.44772 14.5523 7 14 7ZM10 10.5C10.5523 10.5 11 10.9477 11 11.5C11 12.0523 10.5523 12.5 10 12.5C9.44772 12.5 9 12.0523 9 11.5C9 10.9477 9.44772 10.5 10 10.5ZM10 3C8.89543 3 8 3.89543 8 5V6H12V5C12 3.89543 11.1046 3 10 3Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'lock'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M12 2a4 4 0 0 1 4 4v2h1.75A2.25 2.25 0 0 1 20 10.25v9.5A2.25 2.25 0 0 1 17.75 22H6.25A2.25 2.25 0 0 1 4 19.75v-9.5A2.25 2.25 0 0 1 6.25 8H8V6a4 4 0 0 1 4-4Zm5.75 7.5H6.25a.75.75 0 0 0-.75.75v9.5c0 .414.336.75.75.75h11.5a.75.75 0 0 0 .75-.75v-9.5a.75.75 0 0 0-.75-.75Zm-5.75 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm0-10A2.5 2.5 0 0 0 9.5 6v2h5V6A2.5 2.5 0 0 0 12 3.5Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'unlock'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M12 2.004c1.875 0 3.334 1.206 3.928 3.003a.75.75 0 1 1-1.425.47C14.102 4.262 13.185 3.504 12 3.504c-1.407 0-2.42.958-2.496 2.551l-.005.195v1.749h8.251a2.25 2.25 0 0 1 2.245 2.097l.005.154v9.496a2.25 2.25 0 0 1-2.096 2.245l-.154.005H6.25A2.25 2.25 0 0 1 4.005 19.9L4 19.746V10.25a2.25 2.25 0 0 1 2.096-2.245L6.25 8l1.749-.001v-1.75C8 3.712 9.71 2.005 12 2.005ZM17.75 9.5H6.25a.75.75 0 0 0-.743.648l-.007.102v9.496c0 .38.282.693.648.743l.102.007h11.5a.75.75 0 0 0 .743-.648l.007-.102V10.25a.75.75 0 0 0-.648-.744L17.75 9.5Zm-5.75 4a1.499 1.499 0 1 1 0 2.996 1.499 1.499 0 0 1 0-2.997Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'info-help'"
        :width="width"
        :height="height"
        :viewBox="viewbox"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10ZM11 6C11 6.55228 10.5523 7 10 7C9.44772 7 9 6.55228 9 6C9 5.44772 9.44772 5 10 5C10.5523 5 11 5.44772 11 6ZM9 9C8.44772 9 8 9.44772 8 10C8 10.5523 8.44772 11 9 11V14C9 14.5523 9.44772 15 10 15H11C11.5523 15 12 14.5523 12 14C12 13.4477 11.5523 13 11 13V10C11 9.44772 10.5523 9 10 9H9Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'email'"
        width="20"
        height="20"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M1.60201 4.7074L7.99961 7.9058L14.3972 4.7074C14.3735 4.29975 14.1949 3.91659 13.8979 3.63638C13.6009 3.35617 13.2079 3.20012 12.7996 3.2002H3.19961C2.79128 3.20012 2.39837 3.35617 2.10135 3.63638C1.80434 3.91659 1.62569 4.29975 1.60201 4.7074Z"
            fill="white"
        />
        <path
            d="M14.3996 6.4946L7.99961 9.6946L1.59961 6.4946V11.2002C1.59961 11.6245 1.76818 12.0315 2.06824 12.3316C2.3683 12.6316 2.77526 12.8002 3.19961 12.8002H12.7996C13.224 12.8002 13.6309 12.6316 13.931 12.3316C14.231 12.0315 14.3996 11.6245 14.3996 11.2002V6.4946Z"
            fill="white"
        />
    </svg>
    <svg
        v-else-if="name == 'note-plus'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M6 2C4.89543 2 4 2.89543 4 4V9.20703C4.32228 9.11588 4.65659 9.05337 5 9.02242V4C5 3.44772 5.44772 3 6 3H10V6.5C10 7.32843 10.6716 8 11.5 8H15V16C15 16.5523 14.5523 17 14 17H10.4003C10.2174 17.3578 9.99647 17.6929 9.74284 18H14C15.1046 18 16 17.1046 16 16V7.41421C16 7.01639 15.842 6.63486 15.5607 6.35355L11.6464 2.43934C11.3651 2.15804 10.9836 2 10.5858 2H6ZM14.7929 7H11.5C11.2239 7 11 6.77614 11 6.5V3.20711L14.7929 7ZM10 14.5C10 16.9853 7.98528 19 5.5 19C3.01472 19 1 16.9853 1 14.5C1 12.0147 3.01472 10 5.5 10C7.98528 10 10 12.0147 10 14.5ZM6 12.5C6 12.2239 5.77614 12 5.5 12C5.22386 12 5 12.2239 5 12.5V14H3.5C3.22386 14 3 14.2239 3 14.5C3 14.7761 3.22386 15 3.5 15H5L5 16.5C5 16.7761 5.22386 17 5.5 17C5.77614 17 6 16.7761 6 16.5V15H7.5C7.77614 15 8 14.7761 8 14.5C8 14.2239 7.77614 14 7.5 14H6V12.5Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'window-plus'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M14.5 3C15.8807 3 17 4.11929 17 5.5V9.59971C16.6832 9.43777 16.3486 9.30564 16 9.20703V7H4V14.5C4 15.3284 4.67157 16 5.5 16H9.20703C9.30564 16.3486 9.43777 16.6832 9.59971 17H5.5C4.11929 17 3 15.8807 3 14.5V5.5C3 4.11929 4.11929 3 5.5 3H14.5ZM14.5 4H5.5C4.67157 4 4 4.67157 4 5.5V6H16V5.5C16 4.67157 15.3284 4 14.5 4ZM19 14.5C19 16.9853 16.9853 19 14.5 19C12.0147 19 10 16.9853 10 14.5C10 12.0147 12.0147 10 14.5 10C16.9853 10 19 12.0147 19 14.5ZM15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5V14H12.5C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15H14V16.5C14 16.7761 14.2239 17 14.5 17C14.7761 17 15 16.7761 15 16.5V15H16.5C16.7761 15 17 14.7761 17 14.5C17 14.2239 16.7761 14 16.5 14H15V12.5Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'reset'"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M4.12227 0.636347C4.30407 0.818143 4.30407 1.11289 4.12227 1.29469L2.58938 2.82759H8.91379C11.999 2.82759 14.5 5.32862 14.5 8.41379C14.5 11.499 11.999 14 8.91379 14C5.82862 14 3.32759 11.499 3.32759 8.41379C3.32759 8.15669 3.53601 7.94828 3.7931 7.94828C4.0502 7.94828 4.25862 8.15669 4.25862 8.41379C4.25862 10.9848 6.34281 13.069 8.91379 13.069C11.4848 13.069 13.569 10.9848 13.569 8.41379C13.569 5.84281 11.4848 3.75862 8.91379 3.75862H2.58938L4.12227 5.29152C4.30407 5.47332 4.30407 5.76806 4.12227 5.94986C3.94048 6.13166 3.64573 6.13166 3.46393 5.94986L1.13635 3.62227C0.954551 3.44048 0.954551 3.14573 1.13635 2.96393L3.46393 0.636347C3.64573 0.454551 3.94048 0.454551 4.12227 0.636347Z"
            fill="#9CA3AF"
        />
    </svg>
    <svg
        v-else-if="name == 'approve'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M6 2C4.89543 2 4 2.89543 4 4V9.20703C4.32228 9.11588 4.65659 9.05337 5 9.02242V4C5 3.44772 5.44772 3 6 3H10V6.5C10 7.32843 10.6716 8 11.5 8H15V16C15 16.5523 14.5523 17 14 17H10.4003C10.2174 17.3578 9.99647 17.6929 9.74284 18H14C15.1046 18 16 17.1046 16 16V7.41421C16 7.01639 15.842 6.63486 15.5607 6.35355L11.6464 2.43934C11.3651 2.15804 10.9836 2 10.5858 2H6ZM14.7929 7H11.5C11.2239 7 11 6.77614 11 6.5V3.20711L14.7929 7Z"
            :fill="fill"
        ></path>
        <circle cx="5" cy="15" r="4" fill="#4CAF50" />
        <path
            d="M3.5 15L4.55 16.75L7.5 13.75"
            stroke="#fff"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'unapprove'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M6 2C4.89543 2 4 2.89543 4 4V9.20703C4.32228 9.11588 4.65659 9.05337 5 9.02242V4C5 3.44772 5.44772 3 6 3H10V6.5C10 7.32843 10.6716 8 11.5 8H15V16C15 16.5523 14.5523 17 14 17H10.4003C10.2174 17.3578 9.99647 17.6929 9.74284 18H14C15.1046 18 16 17.1046 16 16V7.41421C16 7.01639 15.842 6.63486 15.5607 6.35355L11.6464 2.43934C11.3651 2.15804 10.9836 2 10.5858 2H6ZM14.7929 7H11.5C11.2239 7 11 6.77614 11 6.5V3.20711L14.7929 7Z"
            :fill="fill"
        ></path>
        <circle cx="5" cy="15" r="4" fill="#F44336" />
        <path
            d="M4 14L6 16M6 14L4 16"
            stroke="#fff"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'author'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M10 2C7.79086 2 6 3.79086 6 6C6 8.20914 7.79086 10 10 10C12.2091 10 14 8.20914 14 6C14 3.79086 12.2091 2 10 2ZM7 6C7 4.34315 8.34315 3 10 3C11.6569 3 13 4.34315 13 6C13 7.65685 11.6569 9 10 9C8.34315 9 7 7.65685 7 6ZM5.00873 11C3.90315 11 3 11.8869 3 13C3 14.6912 3.83281 15.9663 5.13499 16.7966C6.41697 17.614 8.14526 18 10 18C11.8547 18 13.583 17.614 14.865 16.7966C16.1672 15.9663 17 14.6912 17 13C17 11.8956 16.1045 11 15 11L5.00873 11ZM4 13C4 12.4467 4.44786 12 5.00873 12L15 12C15.5522 12 16 12.4478 16 13C16 14.3088 15.3777 15.2837 14.3274 15.9534C13.2568 16.636 11.7351 17 10 17C8.26489 17 6.74318 16.636 5.67262 15.9534C4.62226 15.2837 4 14.3088 4 13Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'gte'"
        :width="width"
        :height="height"
        viewBox="0 0 23 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <g clip-path="url(#clip0_47_27887)">
            <path
                d="M7.61914 15.5703C7.34961 15.5703 7.13281 15.3105 7.13281 14.9902C7.13281 14.6699 7.35156 14.4102 7.61914 14.4102H10.2812C10.5508 14.4102 10.7676 14.6699 10.7676 14.9902C10.7676 15.3105 10.5488 15.5703 10.2812 15.5703H7.61914ZM16.7305 13.166C18.2266 13.166 19.5801 13.7715 20.5605 14.752C21.541 15.7324 22.1465 17.0859 22.1465 18.582C22.1465 20.0781 21.541 21.4316 20.5605 22.4121C19.5801 23.3926 18.2266 23.998 16.7305 23.998C15.2344 23.998 13.8809 23.3926 12.9004 22.4121C11.9199 21.4316 11.3145 20.0781 11.3145 18.582C11.3145 17.0859 11.9199 15.7324 12.9004 14.752C13.8809 13.7715 15.2344 13.166 16.7305 13.166ZM16.3164 16.0488C16.3164 15.7813 16.5332 15.5645 16.8008 15.5645C17.0684 15.5645 17.2852 15.7813 17.2852 16.0488V18.5723L19.1719 19.6895C19.4023 19.8242 19.4785 20.1211 19.3418 20.3516C19.207 20.582 18.9102 20.6582 18.6797 20.5215L16.5801 19.2773C16.4238 19.1973 16.3164 19.0352 16.3164 18.8457V16.0488ZM19.877 15.4375C19.0723 14.6328 17.959 14.1348 16.7305 14.1348C15.502 14.1348 14.3887 14.6328 13.584 15.4375C12.7793 16.2422 12.2812 17.3555 12.2812 18.584C12.2812 19.8125 12.7793 20.9258 13.584 21.7305C14.3887 22.5352 15.502 23.0332 16.7305 23.0332C17.959 23.0332 19.0723 22.5352 19.877 21.7305C20.6816 20.9258 21.1797 19.8125 21.1797 18.584C21.1797 17.3555 20.6816 16.2422 19.877 15.4375ZM8.61328 21.4727C8.93359 21.4727 9.19336 21.7324 9.19336 22.0527C9.19336 22.373 8.93359 22.6328 8.61328 22.6328H1.35156C0.980469 22.6328 0.642578 22.4805 0.396484 22.2363C0.152344 21.9922 0 21.6543 0 21.2832V1.35156C0 0.978516 0.152344 0.642578 0.396484 0.396484C0.640625 0.152344 0.978516 0 1.35156 0H17.9336C18.3047 0 18.6426 0.152344 18.8887 0.396484C19.1328 0.640625 19.2852 0.978516 19.2852 1.35156V10.5859C19.2852 10.9063 19.0254 11.166 18.7051 11.166C18.3848 11.166 18.125 10.9063 18.125 10.5859V1.35156C18.125 1.30078 18.1035 1.25391 18.0684 1.21875C18.0332 1.18359 17.9863 1.16211 17.9355 1.16211H1.35156C1.30078 1.16211 1.25195 1.18359 1.21875 1.21875C1.18164 1.25195 1.16016 1.29883 1.16016 1.35156V21.2832C1.16016 21.334 1.18164 21.3828 1.2168 21.416C1.25195 21.4512 1.29883 21.4727 1.34961 21.4727H8.61328ZM3.73438 14.1582H5.18945C5.29492 14.1582 5.38086 14.2441 5.38086 14.3496V15.8047C5.38086 15.9102 5.29492 15.9961 5.18945 15.9961H3.73438C3.62891 15.9961 3.54297 15.9102 3.54297 15.8047V14.3496C3.54492 14.2422 3.63086 14.1582 3.73438 14.1582ZM3.73438 4.19727H5.18945C5.29492 4.19727 5.38086 4.2832 5.38086 4.38867V5.84375C5.38086 5.94922 5.29492 6.03516 5.18945 6.03516H3.73438C3.62891 6.03516 3.54297 5.94922 3.54297 5.84375V4.38867C3.54492 4.2832 3.63086 4.19727 3.73438 4.19727ZM7.61914 5.60938C7.34961 5.60938 7.13281 5.34961 7.13281 5.0293C7.13281 4.70898 7.35156 4.44922 7.61914 4.44922H14.5449C14.8145 4.44922 15.0312 4.70898 15.0312 5.0293C15.0312 5.34961 14.8125 5.60938 14.5449 5.60938H7.61914ZM4.33008 10.9648C4.20508 11.0645 4.02539 11.0391 3.89844 10.916C3.88477 10.9063 3.87109 10.8945 3.85742 10.8809L3.24805 10.252C3.12109 10.1191 3.15039 9.89844 3.31445 9.75781C3.47852 9.61719 3.71484 9.60938 3.84375 9.74219L4.17578 10.084L5.24414 9.22461C5.38672 9.10938 5.60547 9.16016 5.73047 9.33594C5.85547 9.51172 5.83984 9.74805 5.69727 9.86328L4.33008 10.9648ZM7.29883 10.4785C7.0293 10.4785 6.8125 10.2188 6.8125 9.89844C6.8125 9.57813 7.03125 9.31836 7.29883 9.31836H14.2246C14.4941 9.31836 14.7109 9.57813 14.7109 9.89844C14.7109 10.2188 14.4922 10.4785 14.2246 10.4785H7.29883Z"
                fill="currentColor"
            />
        </g>
        <defs>
            <clipPath id="clip0_47_27887">
                <rect width="22.1465" height="24" fill="white" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'prohibit'"
        :width="width"
        :height="height"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
    >
        <path
            fill="red"
            d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2m6.517 4.543L6.543 18.517A8.5 8.5 0 0 0 18.517 6.543M12 3.5a8.5 8.5 0 0 0-6.517 13.957L17.457 5.483A8.47 8.47 0 0 0 12 3.5"
        />
    </svg>
    <svg
        v-else-if="name == 'money'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        v-else-if="name == 'money'" :width="width" :height="height" viewBox="0 0 24 24" fill="none"
        xmlns="http://www.w3.org/2000/svg" >
        <path
            d="M10.5 8C8.84315 8 7.5 9.34315 7.5 11C7.5 12.6569 8.84315 14 10.5 14C12.1569 14 13.5 12.6569 13.5 11C13.5 9.34315 12.1569 8 10.5 8ZM9 11C9 10.1716 9.67157 9.5 10.5 9.5C11.3284 9.5 12 10.1716 12 11C12 11.8284 11.3284 12.5 10.5 12.5C9.67157 12.5 9 11.8284 9 11ZM2 7.25C2 6.00736 3.00736 5 4.25 5H16.75C17.9926 5 19 6.00736 19 7.25V14.75C19 15.9926 17.9926 17 16.75 17H4.25C3.00736 17 2 15.9926 2 14.75V7.25ZM4.25 6.5C3.83579 6.5 3.5 6.83579 3.5 7.25V8H4.25C4.66421 8 5 7.66421 5 7.25V6.5H4.25ZM3.5 12.5H4.25C5.49264 12.5 6.5 13.5074 6.5 14.75V15.5H14.5V14.75C14.5 13.5074 15.5074 12.5 16.75 12.5H17.5V9.5H16.75C15.5074 9.5 14.5 8.49264 14.5 7.25V6.5H6.5V7.25C6.5 8.49264 5.49264 9.5 4.25 9.5H3.5V12.5ZM17.5 8V7.25C17.5 6.83579 17.1642 6.5 16.75 6.5H16V7.25C16 7.66421 16.3358 8 16.75 8H17.5ZM17.5 14H16.75C16.3358 14 16 14.3358 16 14.75V15.5H16.75C17.1642 15.5 17.5 15.1642 17.5 14.75V14ZM3.5 14.75C3.5 15.1642 3.83579 15.5 4.25 15.5H5V14.75C5 14.3358 4.66421 14 4.25 14H3.5V14.75ZM4.40137 18.5C4.92008 19.3967 5.8896 20 7.00002 20H17.25C19.8734 20 22 17.8734 22 15.25V10C22 8.8896 21.3967 7.92008 20.5 7.40137V15.25C20.5 17.0449 19.0449 18.5 17.25 18.5H4.40137Z"
            fill="#10B981"
        />
    </svg>
    <svg
        v-else-if="name == 'hand-money'"
        :width="width"
        :height="height"
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M6.49691 4.9748L21.164 0.97643L23.6665 10.3139L22.4503 10.2151L20.7093 3.73611L20.6904 3.65672C20.6109 3.37597 20.4236 3.14024 20.1698 3.0013C19.916 2.86236 19.6163 2.83158 19.3367 2.91572L18.798 3.07628L18.7934 3.05839L16.2465 3.84446L6.84136 6.2512L6.49691 4.9748ZM5.96405 11.685L5.6972 12.0178C4.98748 12.9061 3.83487 14.3501 3.41493 14.853C3.38895 14.8827 3.35947 14.9092 3.32717 14.9317C3.29912 14.9549 3.26696 14.9725 3.23247 14.9835C3.03838 15.0632 2.85012 15.157 2.66922 15.2642C2.53955 15.3383 2.42166 15.4318 2.3197 15.5416C2.26943 15.596 2.23189 15.661 2.20992 15.7318C2.19272 15.7984 2.19375 15.8683 2.21289 15.9338C2.58689 17.3921 3.01146 18.8922 3.34832 20.3544C3.40884 20.5561 3.5132 20.741 3.65387 20.8959C3.79455 21.0508 3.96802 21.1717 4.16179 21.25C4.52287 21.3997 4.89646 21.5179 5.27847 21.6031C5.67529 21.7272 6.08018 21.8246 6.49051 21.8947C6.90344 21.9573 7.32538 21.9432 7.73555 21.8529L8.96567 21.483C8.98478 21.4738 9.00478 21.4667 9.02533 21.4617C9.06093 21.4518 9.16476 21.423 9.3154 21.3909C10.1098 21.1802 11.0833 20.9182 11.0302 20.203L10.9246 19.7947L10.639 19.858L10.2745 19.9239C10.0767 19.9644 9.89156 20.0045 9.71763 20.0511C9.59961 20.0839 9.47424 20.0682 9.3691 20.0075C9.26397 19.9468 9.18767 19.846 9.157 19.7275C9.12634 19.6089 9.14381 19.4822 9.20557 19.3752C9.26734 19.2682 9.36834 19.1897 9.48636 19.157C9.67325 19.1052 9.88993 19.0579 10.1133 19.0119L10.1408 19.0075C10.894 18.8465 11.7512 18.6696 11.7161 18.0915L11.592 17.6117C11.4782 17.6448 11.3628 17.672 11.2474 17.6992C11.1321 17.7264 10.9595 17.7663 10.8141 17.7939L10.7788 17.7988C10.5858 17.8396 10.4048 17.877 10.2297 17.9255C10.1713 17.9417 10.1104 17.9462 10.0506 17.9387C9.99085 17.9312 9.93328 17.9119 9.88122 17.8819C9.82916 17.8518 9.78364 17.8116 9.74724 17.7636C9.71085 17.7155 9.6843 17.6606 9.66912 17.6019C9.65393 17.5432 9.65041 17.4818 9.65874 17.4214C9.66708 17.3609 9.6871 17.3026 9.71769 17.2496C9.74827 17.1966 9.78881 17.1501 9.83699 17.1127C9.88516 17.0752 9.94004 17.0476 9.99847 17.0314C10.1794 16.9812 10.3931 16.9347 10.6198 16.8831L10.6543 16.8751C11.4064 16.7161 12.2647 16.5372 12.2315 15.9602C12.1872 15.7952 12.1411 15.6291 12.1028 15.4625L10.7737 15.831C10.6557 15.8637 10.5304 15.848 10.4252 15.7873C10.3201 15.7266 10.2438 15.6259 10.2131 15.5073C10.1825 15.3888 10.1999 15.2621 10.2617 15.1551C10.3235 15.0481 10.4245 14.9696 10.5425 14.9369L13.2124 14.1965C13.4581 14.1354 13.6873 14.0178 13.8818 13.8528C14.0429 13.7179 14.1562 13.5338 14.2041 13.329C14.2172 13.2664 14.2231 13.2026 14.2217 13.1389C14.2204 13.0775 14.2121 13.0166 14.197 12.9573C14.1818 12.8969 14.1593 12.8388 14.13 12.7842C14.1001 12.7296 14.0645 12.6784 14.0237 12.6316C13.8828 12.4805 13.6957 12.3814 13.4914 12.3496C13.2344 12.3133 12.9718 12.3348 12.7224 12.4127L6.29469 14.211C6.17667 14.2437 6.05131 14.228 5.94617 14.1673C5.84103 14.1066 5.76474 14.0059 5.73407 13.8873C5.7034 13.7688 5.72087 13.6421 5.78264 13.5351C5.8444 13.4281 5.9454 13.3496 6.06342 13.3169L6.34118 13.2415L5.95548 11.6826L5.96405 11.685ZM7.83008 12.8334L11.6451 11.7755L11.6079 11.7363C11.3499 11.445 11.1837 11.0822 11.1299 10.6932C11.0761 10.3042 11.1372 9.90611 11.3055 9.54845C11.4738 9.1908 11.7419 8.88939 12.0765 8.68173C12.411 8.47406 12.7973 8.36931 13.1871 8.38051C13.577 8.39171 13.9532 8.51837 14.2691 8.74472C14.5849 8.97106 14.8264 9.28711 14.9634 9.65354C15.1005 10.02 15.1271 10.4206 15.0399 10.8055C14.9528 11.1905 14.7557 11.5428 14.4732 11.8186C14.5551 11.8833 14.6312 11.9552 14.7004 12.0335C14.7874 12.1319 14.8629 12.2401 14.9253 12.356C14.988 12.4742 15.0369 12.5994 15.0712 12.7293C15.0858 12.786 15.0975 12.8435 15.1062 12.9016L18.7496 11.8562C18.6696 11.522 18.7239 11.1675 18.9006 10.8695C19.0773 10.5715 19.3623 10.354 19.6938 10.264L18.8598 6.89219C18.5269 6.97879 18.1756 6.92994 17.8822 6.75624C17.5887 6.58254 17.3768 6.29802 17.2924 5.96447L8.12203 8.63351C8.20589 8.96662 8.15578 9.32162 7.98256 9.62165C7.80934 9.92167 7.52696 10.1426 7.19655 10.2365L7.83943 12.8388L7.83008 12.8334ZM14.524 14.5228C14.2128 14.8095 13.8373 15.0151 13.4309 15.1214L12.9959 15.2404L13.1301 15.759L13.1402 15.8105L13.1455 15.8617C13.1903 16.1379 13.1482 16.4227 13.0251 16.6762C12.9019 16.9296 12.704 17.1392 12.4588 17.2755L12.4682 17.2809C12.4813 17.3084 12.4923 17.3368 12.5013 17.3659L12.6313 17.8745C12.6368 17.8911 12.6403 17.9084 12.6414 17.926L12.647 17.9723C12.6948 18.3035 12.6243 18.6428 12.448 18.9299C12.2942 19.162 12.0815 19.3485 11.8325 19.4695L11.9639 19.9713L11.9729 20.0247L11.9767 20.07C12.1289 21.5929 10.7111 21.9685 9.56699 22.2714C9.48437 22.3023 9.3623 22.3281 9.27034 22.3536C8.86029 22.4769 8.34303 22.6651 7.94308 22.7536C6.8208 23.0057 5.99517 22.7699 5.04018 22.4949C4.59958 22.3972 4.16991 22.2569 3.75708 22.0761C3.44947 21.9417 3.17591 21.7404 2.95579 21.4862C2.73567 21.2321 2.57436 20.9314 2.48329 20.6054L1.34391 16.2003C1.28108 15.9877 1.27516 15.7613 1.32673 15.5439C1.38599 15.3232 1.49685 15.1193 1.64992 14.9496C1.7928 14.7897 1.95697 14.6507 2.1376 14.5364C2.34971 14.3998 2.5729 14.2818 2.80466 14.1838C3.11391 13.8121 3.87612 12.8502 4.58146 11.9694C5.00039 11.438 5.3977 10.9397 5.68772 10.5798L5.06687 8.06884L19.7041 3.93661L21.8122 12.4683L14.524 14.5228Z"
            fill="#6366F1"
        />
    </svg>
    <svg
        v-else-if="name == 'student'"
        :width="width"
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M4.09668 6.40625C3.75814 6.30208 3.58887 6.04167 3.58887 5.625C3.58887 5.20833 3.75814 4.94792 4.09668 4.84375L11.6748 2.57812C11.8831 2.52604 12.1045 2.5 12.3389 2.5C12.5732 2.5 12.7946 2.52604 13.0029 2.57812L20.5811 4.84375C20.9196 4.94792 21.0889 5.20833 21.0889 5.625C21.0889 6.04167 20.9196 6.30208 20.5811 6.40625L17.5342 7.30469C17.8206 7.98177 17.9639 8.67188 17.9639 9.375C17.9639 10.9375 17.417 12.2656 16.3232 13.3594C15.2295 14.4531 13.9014 15 12.3389 15C10.7764 15 9.44824 14.4531 8.35449 13.3594C7.26074 12.2656 6.71387 10.9375 6.71387 9.375C6.71387 8.67188 6.8571 7.98177 7.14355 7.30469L5.30762 6.75781V7.96875C5.62012 8.15104 5.77637 8.41146 5.77637 8.75C5.77637 9.0625 5.63314 9.32292 5.34668 9.53125L5.93262 11.9531C5.95866 12.0573 5.95866 12.1484 5.93262 12.2266C5.93262 12.3047 5.90658 12.3698 5.85449 12.4219C5.80241 12.474 5.7373 12.5 5.65918 12.5H4.01855C3.91439 12.5 3.82324 12.4479 3.74512 12.3438C3.69303 12.2135 3.69303 12.0833 3.74512 11.9531L4.33105 9.53125C4.0446 9.32292 3.90137 9.0625 3.90137 8.75C3.90137 8.41146 4.05762 8.15104 4.37012 7.96875V6.48438L4.09668 6.40625ZM12.3389 13.125C13.3805 13.125 14.266 12.7604 14.9951 12.0312C15.7243 11.3021 16.0889 10.4167 16.0889 9.375C16.0889 8.85417 15.9717 8.34635 15.7373 7.85156L13.0029 8.67188C12.5602 8.80208 12.1175 8.80208 11.6748 8.67188L8.94043 7.85156C8.70605 8.34635 8.58887 8.85417 8.58887 9.375C8.58887 10.4167 8.95345 11.3021 9.68262 12.0312C10.4118 12.7604 11.2972 13.125 12.3389 13.125ZM12.2217 4.375L8.04199 5.625L12.2217 6.875C12.2998 6.875 12.3779 6.875 12.4561 6.875L16.6357 5.625L12.4561 4.375C12.3779 4.375 12.2998 4.375 12.2217 4.375ZM16.0498 15.0391C17.4561 15.0911 18.641 15.625 19.6045 16.6406C20.5941 17.6302 21.0889 18.8281 21.0889 20.2344V20.625C21.0889 21.1458 20.9066 21.5885 20.542 21.9531C20.1774 22.3177 19.7347 22.5 19.2139 22.5H5.46387C4.94303 22.5 4.50033 22.3177 4.13574 21.9531C3.77116 21.5885 3.58887 21.1458 3.58887 20.625V20.2344C3.58887 18.8281 4.07064 17.6302 5.03418 16.6406C6.02376 15.625 7.22168 15.0911 8.62793 15.0391L12.3389 18.125L16.0498 15.0391ZM11.4014 20.625V19.7656L8.04199 16.9922C7.28678 17.1745 6.66178 17.5651 6.16699 18.1641C5.69824 18.763 5.46387 19.4531 5.46387 20.2344V20.625H11.4014ZM19.2139 20.625V20.2344C19.2139 19.4531 18.9665 18.763 18.4717 18.1641C18.0029 17.5651 17.391 17.1745 16.6357 16.9922L13.2764 19.7656V20.625H19.2139Z"
            fill="#1890FF"
        />
    </svg>
    <svg
        v-else-if="name == 'exchange'"
        :width="width"
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M14.9123 2.28878L14.825 2.21113C14.4325 1.90052 13.8607 1.92641 13.498 2.28878L13.4203 2.37594C13.1095 2.76818 13.1354 3.33963 13.498 3.702L14.797 4.99899L9.19916 4.99919L8.95854 5.00325C5.2044 5.12998 2.19995 8.21112 2.19995 11.9935C2.19995 13.4382 2.63828 14.7806 3.38858 15.8918C3.57019 16.1432 3.86595 16.3068 4.19995 16.3068C4.75224 16.3068 5.19995 15.8594 5.19995 15.3075C5.19995 15.0914 5.13127 14.8912 5.0152 14.7288L4.88003 14.5107C4.4477 13.7716 4.19995 12.9114 4.19995 11.9935C4.19995 9.23444 6.43818 6.99779 9.19916 6.99779L14.795 6.99758L13.498 8.29497L13.4203 8.38213C13.1095 8.77438 13.1354 9.34582 13.498 9.7082C13.8886 10.0984 14.5217 10.0984 14.9123 9.7082L17.9175 6.7051L17.9952 6.61794C18.306 6.2257 18.2801 5.65425 17.9175 5.29188L14.9123 2.28878ZM20.9864 8.06013C20.8033 7.82751 20.5191 7.67811 20.2 7.67811C19.6477 7.67811 19.2 8.12551 19.2 8.67741C19.2 8.88559 19.2637 9.0789 19.3716 9.23841C19.8951 10.0282 20.2 10.9753 20.2 11.9935C20.2 14.7525 17.9617 16.9892 15.2007 16.9892L9.61495 16.9886L10.9086 15.6972L10.9923 15.6025C11.2732 15.2408 11.2713 14.7307 10.9863 14.3712L10.9086 14.284L10.8138 14.2004C10.4519 13.9196 9.94145 13.9216 9.58164 14.2063L9.49442 14.284L6.48921 17.2871L6.40557 17.3818C6.12461 17.7435 6.12658 18.2536 6.41151 18.6132L6.48921 18.7003L9.49442 21.7034L9.58863 21.7865C9.98092 22.0913 10.5481 22.0636 10.9086 21.7034C11.2713 21.341 11.2972 20.7696 10.9863 20.3773L10.9086 20.2902L9.60495 18.9872L15.2007 18.9878L15.4414 18.9837C19.1955 18.857 22.2 15.7759 22.2 11.9935C22.2 10.5336 21.7523 9.17809 20.9868 8.05666L20.9864 8.06013Z"
            fill="#F59E0B"
        />
    </svg>
    <svg
        v-else-if="name == 'vis-rejection'"
        :width="width"
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <g clip-path="url(#clip0_209_20433)">
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M9.59988 3.02148C11.5774 3.02148 13.1797 4.62429 13.1797 6.60134C13.1797 8.57886 11.5765 10.1812 9.59988 10.1812C7.62236 10.1812 6.02002 8.57792 6.02002 6.60134C6.02002 4.62336 7.6233 3.02148 9.59988 3.02148ZM8.08138 5.91844C7.95538 5.79432 7.85421 5.7161 8.01206 5.56294L8.52119 5.06598C8.68278 4.90251 8.7774 4.91095 8.92775 5.06504L9.61393 5.75123L10.2964 5.06926C10.421 4.9428 10.4987 4.84162 10.6519 4.99947L11.1493 5.50907C11.3123 5.6702 11.3039 5.76481 11.1502 5.91516L10.464 6.60134L11.1502 7.28753C11.3039 7.43741 11.3123 7.53202 11.1493 7.69362L10.6519 8.20275C10.4987 8.36059 10.421 8.25942 10.2964 8.13343L9.61393 7.45099L8.92775 8.13764C8.7774 8.29174 8.68278 8.30017 8.52119 8.13671L8.01206 7.63928C7.85421 7.48612 7.95538 7.40837 8.08138 7.28378L8.76428 6.60134L8.08138 5.91844Z"
                fill="#F44336"
            />
            <path
                d="M12.4626 21.5443L16.6579 17.1349H12.4626V21.5443ZM3.90663 17.5836H7.66167V18.6632H3.90663V17.5836ZM3.90663 14.8403H9.23637V15.9204H3.90663V14.8403ZM3.90663 12.0979H15.293V13.178H3.90663V12.0979ZM18.9909 16.5714L12.3038 23.7429C12.2388 23.8232 12.1566 23.8879 12.0634 23.9324C11.9701 23.9769 11.8681 24 11.7647 24H1.46448C0.769864 24 0.20874 23.4389 0.20874 22.7443V1.25527C0.20874 0.562529 0.770801 0 1.46448 0H17.7357C18.4289 0 18.9909 0.562529 18.9909 1.25527V16.5714ZM17.7652 15.9091V1.22576H1.4345V22.7803H11.2368V16.6131C11.2368 16.2276 11.5493 15.9091 11.9413 15.9091H17.7652Z"
                fill="#EF4444"
            />
        </g>
        <defs>
            <clipPath id="clip0_209_20433">
                <rect width="18.7822" height="24" fill="white" transform="translate(0.20874)" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'reinvite'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <g clip-path="url(#clip0_676_9472)">
            <path
                d="M5.77344 5H22.9648C23.2715 5 23.541 5.125 23.7266 5.32617C23.9141 5.52734 24.0195 5.80664 23.9961 6.11328L23.1328 17.9277C23.1152 18.1582 23.0293 18.373 22.8945 18.5527C22.8672 18.6113 22.8301 18.6641 22.7793 18.709C22.7461 18.7383 22.709 18.7637 22.6699 18.7813C22.4648 18.9434 22.209 19.041 21.9395 19.041H4.74805C4.44141 19.041 4.17188 18.916 3.98438 18.7148C3.79688 18.5137 3.69336 18.2344 3.71484 17.9277L3.95898 15.9395H4.8125L4.60156 17.459L11.0684 11.418L5.4043 6.50781L5.34766 7.27539H4.49219L4.57617 6.11328C4.59766 5.80664 4.74414 5.52734 4.96094 5.32617C5.17773 5.125 5.4668 5 5.77344 5ZM0.427734 14.7227C0.191406 14.7227 0 14.5313 0 14.2949C0 14.0586 0.191406 13.8672 0.427734 13.8672H6.07031C6.30664 13.8672 6.49805 14.0586 6.49805 14.2949C6.49805 14.5313 6.30664 14.7227 6.07031 14.7227H0.427734ZM2.54297 12.0742C2.30664 12.0742 2.11523 11.8828 2.11523 11.6465C2.11523 11.4102 2.30664 11.2188 2.54297 11.2188H7.4375C7.67383 11.2188 7.86523 11.4102 7.86523 11.6465C7.86523 11.8828 7.67383 12.0742 7.4375 12.0742H2.54297ZM0.427734 9.42578C0.191406 9.42578 0 9.23438 0 8.99805C0 8.76172 0.191406 8.57031 0.427734 8.57031H6.07031C6.30664 8.57031 6.49805 8.76172 6.49805 8.99805C6.49805 9.23438 6.30664 9.42578 6.07031 9.42578H0.427734ZM11.6934 11.9609L5.02734 18.1875H21.7324L16.25 11.9609L14.1719 13.6289C14.0098 13.7598 13.7793 13.7676 13.625 13.6348L11.6934 11.9609ZM16.9414 11.4082L22.3125 17.5078L23.1191 6.45313L16.9414 11.4082ZM5.99805 5.85352L13.9531 12.75L22.5527 5.85352H5.99805Z"
                :fill="fill"
            />
        </g>
        <defs>
            <clipPath id="clip0_676_9472">
                <rect width="24" height="14.043" fill="white" transform="translate(0 5)" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'eye'"
        :width="width"
        :height="height"
        viewBox="0 0 22 16"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            d="M13.9998 8C13.9998 9.65685 12.6566 11 10.9998 11C9.3429 11 7.99976 9.65685 7.99976 8C7.99976 6.34315 9.3429 5 10.9998 5C12.6566 5 13.9998 6.34315 13.9998 8Z"
            stroke="#6B7280"
            stroke-width="1.2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
        <path
            d="M1.45801 7.99997C2.73228 3.94288 6.52257 1 11.0002 1C15.4778 1 19.2681 3.94291 20.5424 8.00004C19.2681 12.0571 15.4778 15 11.0002 15C6.52256 15 2.73226 12.0571 1.45801 7.99997Z"
            stroke="#6B7280"
            stroke-width="1.2"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
    <svg
        v-else-if="name == 'arrow-right'"
        :width="width"
        :height="height"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M14.7499 6.35147C15.2185 5.88284 15.9783 5.88284 16.447 6.35147L21.247 11.1515C21.7156 11.6201 21.7156 12.3799 21.247 12.8485L16.447 17.6485C15.9783 18.1172 15.2185 18.1172 14.7499 17.6485C14.2813 17.1799 14.2813 16.4201 14.7499 15.9515L17.5014 13.2H3.59844C2.9357 13.2 2.39844 12.6627 2.39844 12C2.39844 11.3373 2.9357 10.8 3.59844 10.8H17.5014L14.7499 8.04853C14.2813 7.5799 14.2813 6.8201 14.7499 6.35147Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'prev-arrow'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M6.25 4.75C5.8703 4.75 5.55651 5.03215 5.50685 5.39823L5.5 5.5L5.5 14.5C5.5 14.9142 5.83579 15.25 6.25 15.25C6.62969 15.25 6.94349 14.9678 6.99315 14.6018L7 14.5L7 5.5C7 5.08579 6.66421 4.75 6.25 4.75ZM14.5303 4.96967C14.2641 4.7034 13.8474 4.6792 13.5538 4.89705L13.4697 4.96967L8.96967 9.46967C8.7034 9.73594 8.6792 10.1526 8.89705 10.4462L8.96967 10.5303L13.4697 15.0303C13.7626 15.3232 14.2374 15.3232 14.5303 15.0303C14.7966 14.7641 14.8208 14.3474 14.6029 14.0538L14.5303 13.9697L10.5607 10L14.5303 6.03033C14.8232 5.73744 14.8232 5.26256 14.5303 4.96967Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'chevron'"
        width="26"
        height="28"
        viewBox="0 0 26 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M9.35067 20.7597C9.70058 21.093 10.2544 21.0795 10.5877 20.7296L16.4226 14.604C16.7445 14.2661 16.7445 13.735 16.4226 13.397L10.5877 7.2715C10.2544 6.92159 9.70058 6.90813 9.35067 7.24143C9.00076 7.57474 8.9873 8.1286 9.3206 8.47851L14.5806 14.0005L9.3206 19.5226C8.9873 19.8725 9.00076 20.4264 9.35067 20.7597Z"
            fill="white"
        />
    </svg>
    <svg
        v-else-if="name == 'undefined'"
        :width="width"
        :height="height"
        viewBox="0 0 20 20"
        :fill="bgfill"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2ZM10 12.5C9.58579 12.5 9.25 12.8358 9.25 13.25C9.25 13.6642 9.58579 14 10 14C10.4142 14 10.75 13.6642 10.75 13.25C10.75 12.8358 10.4142 12.5 10 12.5ZM10 6C9.75454 6 9.55039 6.17688 9.50806 6.41012L9.5 6.5V11L9.50806 11.0899C9.55039 11.3231 9.75454 11.5 10 11.5C10.2455 11.5 10.4496 11.3231 10.4919 11.0899L10.5 11V6.5L10.4919 6.41012C10.4496 6.17688 10.2455 6 10 6Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'regenerate'"
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        :fill="bgfill"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M4.12227 0.636347C4.30407 0.818143 4.30407 1.11289 4.12227 1.29469L2.58938 2.82759H8.91379C11.999 2.82759 14.5 5.32862 14.5 8.41379C14.5 11.499 11.999 14 8.91379 14C5.82862 14 3.32759 11.499 3.32759 8.41379C3.32759 8.15669 3.53601 7.94828 3.7931 7.94828C4.0502 7.94828 4.25862 8.15669 4.25862 8.41379C4.25862 10.9848 6.34281 13.069 8.91379 13.069C11.4848 13.069 13.569 10.9848 13.569 8.41379C13.569 5.84281 11.4848 3.75862 8.91379 3.75862H2.58938L4.12227 5.29152C4.30407 5.47332 4.30407 5.76806 4.12227 5.94986C3.94048 6.13166 3.64573 6.13166 3.46393 5.94986L1.13635 3.62227C0.954551 3.44048 0.954551 3.14573 1.13635 2.96393L3.46393 0.636347C3.64573 0.454551 3.94048 0.454551 4.12227 0.636347Z"
            fill="currentColor"
        ></path>
    </svg>
    <svg
        v-else-if="name == 'search'"
        :width="width"
        :height="height"
        :fill="bgfill"
        viewBox="0 0 19 19"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M8 0.75C12.0041 0.75 15.25 3.99594 15.25 8C15.25 9.73187 14.6427 11.3219 13.6295 12.5688L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2641 18.7966 17.8474 18.8208 17.5538 18.6029L17.4697 18.5303L12.5688 13.6295C11.3219 14.6427 9.73187 15.25 8 15.25C3.99594 15.25 0.75 12.0041 0.75 8C0.75 3.99594 3.99594 0.75 8 0.75ZM8 2.25C4.82436 2.25 2.25 4.82436 2.25 8C2.25 11.1756 4.82436 13.75 8 13.75C11.1756 13.75 13.75 11.1756 13.75 8C13.75 4.82436 11.1756 2.25 8 2.25Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'user'"
        :width="width"
        :height="height"
        :fill="bgfill"
        viewBox="0 0 19 18"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M9.33355 2C7.36987 2 5.77799 3.59188 5.77799 5.55556C5.77799 7.51923 7.36987 9.11111 9.33355 9.11111C11.2972 9.11111 12.8891 7.51923 12.8891 5.55556C12.8891 3.59188 11.2972 2 9.33355 2ZM6.66688 5.55556C6.66688 4.0828 7.86079 2.88889 9.33355 2.88889C10.8063 2.88889 12.0002 4.0828 12.0002 5.55556C12.0002 7.02832 10.8063 8.22222 9.33355 8.22222C7.86079 8.22222 6.66688 7.02832 6.66688 5.55556ZM4.89686 10C3.91413 10 3.11133 10.7883 3.11133 11.7778C3.11133 13.2811 3.8516 14.4145 5.0091 15.1525C6.14863 15.8791 7.68489 16.2222 9.33355 16.2222C10.9822 16.2222 12.5185 15.8791 13.658 15.1525C14.8155 14.4145 15.5558 13.2811 15.5558 11.7778C15.5558 10.7961 14.7598 10 13.778 10L4.89686 10ZM4.00022 11.7778C4.00022 11.286 4.39832 10.8889 4.89686 10.8889L13.778 10.8889C14.2688 10.8889 14.6669 11.287 14.6669 11.7778C14.6669 12.9412 14.1138 13.8077 13.1801 14.403C12.2285 15.0098 10.8759 15.3333 9.33355 15.3333C7.79123 15.3333 6.4386 15.0098 5.48699 14.403C4.55334 13.8077 4.00022 12.9412 4.00022 11.7778Z"
            fill="currentColor"
        />
    </svg>
    <svg
        v-else-if="name == 'submit-success'"
        width="304"
        height="260"
        viewBox="0 0 304 260"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <g clip-path="url(#clip0_1826_146698)">
            <path
                d="M23.2536 238.6C24.9995 239.213 26.8741 239.367 28.6966 239.046C30.5192 238.725 32.2284 237.94 33.6596 236.767C37.3042 233.707 38.4472 228.669 39.3767 224.002L42.1269 210.199L36.3692 214.164C32.2284 217.015 27.9945 219.957 25.1277 224.087C22.2609 228.216 21.0102 233.854 23.3129 238.322"
                fill="#E6E6E6"
            />
            <path
                d="M24.1443 256.271C23.4196 250.992 22.674 245.644 23.1835 240.308C23.6351 235.568 25.081 230.94 28.0247 227.144C29.5872 225.133 31.4885 223.41 33.643 222.053C34.2047 221.698 34.7218 222.588 34.1625 222.941C30.4345 225.297 27.5509 228.776 25.9275 232.877C24.1347 237.437 23.8468 242.407 24.1556 247.251C24.3423 250.181 24.7389 253.091 25.1373 255.997C25.1688 256.129 25.1491 256.267 25.0824 256.385C25.0156 256.502 24.9067 256.59 24.7777 256.63C24.6461 256.666 24.5058 256.648 24.3873 256.581C24.2688 256.513 24.1816 256.402 24.1447 256.271L24.1443 256.271Z"
                fill="#F2F2F2"
            />
            <path
                d="M29.3594 247.801C30.1081 248.939 31.1373 249.865 32.3479 250.49C33.5585 251.115 34.9097 251.418 36.2713 251.369C39.7712 251.203 42.6877 248.762 45.313 246.443L53.0796 239.585L47.9396 239.339C44.2431 239.162 40.4512 238.996 36.9304 240.137C33.4096 241.279 30.1626 244.021 29.5189 247.665"
                fill="#E6E6E6"
            />
            <path
                d="M22.0911 259.307C25.58 253.133 29.6265 246.273 36.8575 244.08C38.8679 243.472 40.976 243.254 43.0681 243.438C43.7276 243.495 43.5629 244.512 42.9048 244.455C39.3978 244.164 35.8975 245.09 32.9933 247.077C30.1985 248.98 28.0224 251.624 26.1809 254.429C25.0527 256.147 24.0422 257.937 23.0317 259.726C22.7088 260.297 21.7645 259.885 22.0911 259.307Z"
                fill="#F2F2F2"
            />
            <path
                d="M301.255 183.099H108.908C108.315 183.098 107.747 182.863 107.328 182.444C106.908 182.025 106.673 181.456 106.672 180.864V2.23564C106.673 1.64295 106.908 1.07475 107.328 0.655659C107.747 0.236564 108.315 0.000774795 108.908 0H301.255C301.848 0.000774795 302.416 0.236564 302.835 0.655659C303.255 1.07475 303.49 1.64295 303.491 2.23564V180.863C303.49 181.456 303.255 182.024 302.836 182.443C302.416 182.862 301.848 183.099 301.255 183.099Z"
                fill="white"
            />
            <path
                d="M301.255 183.099H108.908C108.315 183.098 107.747 182.863 107.328 182.444C106.908 182.025 106.673 181.456 106.672 180.864V2.23564C106.673 1.64295 106.908 1.07475 107.328 0.655659C107.747 0.236564 108.315 0.000774795 108.908 0H301.255C301.848 0.000774795 302.416 0.236564 302.835 0.655659C303.255 1.07475 303.49 1.64295 303.491 2.23564V180.863C303.49 181.456 303.255 182.024 302.836 182.443C302.416 182.862 301.848 183.099 301.255 183.099ZM108.908 0.892495C108.552 0.893266 108.212 1.03472 107.961 1.28589C107.71 1.53706 107.568 1.8775 107.567 2.23271V180.863C107.568 181.218 107.71 181.559 107.961 181.81C108.212 182.061 108.552 182.203 108.908 182.203H301.255C301.611 182.203 301.951 182.061 302.202 181.81C302.454 181.559 302.595 181.218 302.596 180.863V2.23564C302.595 1.88043 302.453 1.53999 302.202 1.28882C301.951 1.03765 301.611 0.8962 301.255 0.895429L108.908 0.892495Z"
                fill="#CACACA"
            />
            <path
                d="M199.796 46.418C199.394 46.4187 199.009 46.5789 198.725 46.8634C198.441 47.148 198.281 47.5337 198.281 47.9357C198.281 48.3378 198.441 48.7235 198.725 49.0081C199.009 49.2926 199.394 49.4528 199.796 49.4535H283.08C283.482 49.4528 283.868 49.2926 284.152 49.0081C284.436 48.7235 284.596 48.3378 284.596 47.9357C284.596 47.5337 284.436 47.148 284.152 46.8634C283.868 46.5789 283.482 46.4187 283.08 46.418H199.796Z"
                fill="#E4E4E4"
            />
            <path
                d="M199.795 55.5254C199.393 55.5264 199.008 55.6867 198.725 55.9712C198.441 56.2558 198.281 56.6413 198.281 57.0432C198.281 57.4451 198.441 57.8306 198.725 58.1151C199.008 58.3997 199.393 58.56 199.795 58.561H242.313C242.715 58.56 243.101 58.3997 243.384 58.1151C243.668 57.8306 243.828 57.4451 243.828 57.0432C243.828 56.6413 243.668 56.2558 243.384 55.9712C243.101 55.6867 242.715 55.5264 242.313 55.5254H199.795Z"
                fill="#E4E4E4"
            />
            <path
                d="M127.006 104.502C126.604 104.503 126.219 104.663 125.935 104.948C125.652 105.232 125.492 105.618 125.492 106.02C125.492 106.422 125.652 106.807 125.935 107.092C126.219 107.376 126.604 107.537 127.006 107.538H283.164C283.566 107.537 283.951 107.376 284.235 107.092C284.519 106.807 284.678 106.422 284.678 106.02C284.678 105.618 284.519 105.232 284.235 104.948C283.951 104.663 283.566 104.503 283.164 104.502H127.006Z"
                fill="#E4E4E4"
            />
            <path
                d="M127.006 113.609C126.604 113.61 126.219 113.771 125.935 114.055C125.652 114.34 125.492 114.725 125.492 115.127C125.492 115.529 125.652 115.915 125.935 116.199C126.219 116.484 126.604 116.644 127.006 116.645H242.398C242.8 116.644 243.185 116.484 243.469 116.199C243.753 115.915 243.912 115.529 243.912 115.127C243.912 114.725 243.753 114.34 243.469 114.055C243.185 113.771 242.8 113.61 242.398 113.609H127.006Z"
                fill="#E4E4E4"
            />
            <path
                d="M127.006 122.512C126.604 122.513 126.219 122.673 125.935 122.958C125.652 123.242 125.492 123.628 125.492 124.029C125.492 124.431 125.652 124.817 125.935 125.101C126.219 125.386 126.604 125.546 127.006 125.547H283.164C283.566 125.546 283.951 125.386 284.235 125.101C284.519 124.817 284.678 124.431 284.678 124.029C284.678 123.628 284.519 123.242 284.235 122.958C283.951 122.673 283.566 122.513 283.164 122.512H127.006Z"
                fill="#E4E4E4"
            />
            <path
                d="M127.006 131.619C126.604 131.62 126.219 131.78 125.935 132.065C125.652 132.35 125.492 132.735 125.492 133.137C125.492 133.539 125.652 133.924 125.935 134.209C126.219 134.493 126.604 134.654 127.006 134.655H242.398C242.8 134.654 243.185 134.493 243.469 134.209C243.753 133.924 243.912 133.539 243.912 133.137C243.912 132.735 243.753 132.35 243.469 132.065C243.185 131.78 242.8 131.62 242.398 131.619H127.006Z"
                fill="#E4E4E4"
            />
            <path
                d="M127.006 140.758C126.604 140.759 126.219 140.919 125.935 141.204C125.652 141.488 125.492 141.874 125.492 142.276C125.492 142.677 125.652 143.063 125.935 143.348C126.219 143.632 126.604 143.792 127.006 143.793H283.164C283.566 143.792 283.951 143.632 284.235 143.348C284.519 143.063 284.678 142.677 284.678 142.276C284.678 141.874 284.519 141.488 284.235 141.204C283.951 140.919 283.566 140.759 283.164 140.758H127.006Z"
                fill="#E4E4E4"
            />
            <path
                d="M127.006 149.865C126.604 149.866 126.219 150.027 125.935 150.311C125.652 150.596 125.492 150.981 125.492 151.383C125.492 151.785 125.652 152.17 125.935 152.455C126.219 152.739 126.604 152.9 127.006 152.901H242.398C242.8 152.9 243.185 152.739 243.469 152.455C243.753 152.17 243.912 151.785 243.912 151.383C243.912 150.981 243.753 150.596 243.469 150.311C243.185 150.027 242.8 149.866 242.398 149.865H127.006Z"
                fill="#E4E4E4"
            />
            <path
                d="M151.736 74.2945C147.423 74.2945 143.207 73.0156 139.621 70.6195C136.035 68.2234 133.24 64.8177 131.59 60.8331C129.939 56.8485 129.507 52.464 130.349 48.234C131.19 44.0039 133.267 40.1184 136.317 37.0687C139.366 34.019 143.252 31.9421 147.482 31.1007C151.712 30.2592 156.096 30.6911 160.081 32.3415C164.066 33.992 167.471 36.787 169.867 40.373C172.264 43.9591 173.543 48.1751 173.543 52.4881C173.536 58.2694 171.236 63.8121 167.148 67.9002C163.06 71.9883 157.518 74.2878 151.736 74.2945Z"
                fill="#096DD9"
            />
            <path
                d="M85.5411 156.746C85.5034 155.95 85.2916 155.172 84.9207 154.467C84.5498 153.761 84.0288 153.146 83.3944 152.664C82.7599 152.182 82.0275 151.845 81.2486 151.676C80.4698 151.508 79.6635 151.512 78.8866 151.689L71.6787 141.531L64.4375 144.406L74.8179 158.608C75.2016 159.893 76.0435 160.993 77.1841 161.7C78.3248 162.406 79.6848 162.669 81.0066 162.44C82.3284 162.21 83.52 161.504 84.3558 160.454C85.1915 159.405 85.6133 158.085 85.5411 156.746Z"
                fill="#FFB9B9"
            />
            <path
                d="M71.4974 155.994L49.6875 127.7L57.8595 102.017C58.4583 95.5595 62.4989 93.7565 62.6708 93.6828L62.9331 93.5703L70.0442 112.534L64.823 126.457L77.6383 148.011L71.4974 155.994Z"
                fill="#3F3D56"
            />
            <path
                d="M147.238 68.7737C146.447 68.8728 145.688 69.1441 145.013 69.5684C144.339 69.9927 143.766 70.5598 143.334 71.2296C142.903 71.8994 142.623 72.6557 142.515 73.4452C142.408 74.2348 142.474 75.0383 142.711 75.7992L133.141 83.7709L136.567 90.7682L149.923 79.3208C151.175 78.8389 152.207 77.9144 152.823 76.7226C153.439 75.5308 153.596 74.1545 153.265 72.8544C152.934 71.5543 152.138 70.4208 151.027 69.6687C149.916 68.9166 148.568 68.5982 147.238 68.7737Z"
                fill="#FFB9B9"
            />
            <path
                d="M147.574 82.8374L121.051 106.77L94.8133 100.608C88.3292 100.51 86.2192 96.6209 86.1324 96.4552L86 96.2024L104.357 87.6464L118.642 91.7755L139.14 77.332L147.574 82.8374Z"
                fill="#3F3D56"
            />
            <path
                d="M101.634 252.972L108.76 252.971L112.15 225.484L101.633 225.485L101.634 252.972Z"
                fill="#FFB9B9"
            />
            <path
                d="M122.5 259.588L100.103 259.589L100.103 250.936L116.216 250.935C117.883 250.935 119.481 251.597 120.659 252.775C121.838 253.953 122.5 255.552 122.5 257.218L122.5 259.588Z"
                fill="#2F2E41"
            />
            <path
                d="M73.1493 252.972L80.2756 252.971L83.6658 225.484L73.1484 225.485L73.1493 252.972Z"
                fill="#FFB9B9"
            />
            <path
                d="M94.0234 259.588L71.6269 259.589L71.6265 250.936L87.7396 250.935C88.5648 250.935 89.3819 251.097 90.1442 251.413C90.9066 251.729 91.5993 252.192 92.1828 252.775C92.7663 253.359 93.2291 254.051 93.5449 254.814C93.8607 255.576 94.0233 256.393 94.0233 257.218L94.0234 259.588Z"
                fill="#2F2E41"
            />
            <path
                d="M70.6172 152.641L71.1984 193.329L71.78 246.806L84.5675 245.643L90.9617 171.823L99.0995 245.643H112.301L113.631 171.241L108.981 154.966L70.6172 152.641Z"
                fill="#2F2E41"
            />
            <path
                d="M95.6474 158.104C81.7334 158.105 68.9258 151.808 68.7529 151.721L68.6093 151.649L67.4416 123.628C67.1031 122.637 60.4357 103.098 59.3065 96.8875C58.1624 90.5953 74.7439 85.0731 76.7574 84.4273L77.2144 79.3655L95.796 77.3633L98.1511 83.84L104.817 86.3397C105.573 86.6233 106.199 87.1743 106.575 87.8882C106.952 88.6021 107.054 89.4294 106.861 90.2133L103.156 105.28L112.205 155.129L110.257 155.213C105.587 157.322 100.547 158.104 95.6474 158.104Z"
                fill="#3F3D56"
            />
            <path
                d="M93.6441 72.5182C100.128 68.9741 102.51 60.8451 98.9661 54.3617C95.4219 47.8783 87.293 45.4956 80.8096 49.0397C74.3262 52.5839 71.9434 60.7129 75.4876 67.1963C79.0318 73.6797 87.1607 76.0624 93.6441 72.5182Z"
                fill="#FFB9B9"
            />
            <path
                d="M76.396 73.7315C78.9457 76.4475 83.6808 74.9895 84.013 71.2791C84.0391 70.991 84.0372 70.7011 84.0074 70.4134C83.8359 68.7699 82.8864 67.2778 83.1138 65.5424C83.1653 65.1105 83.3262 64.6989 83.5813 64.3466C85.613 61.6259 90.3825 65.5635 92.3 63.1005C93.4758 61.5903 92.0937 59.2125 92.9959 57.5245C94.1868 55.2967 97.7141 56.3957 99.926 55.1756C102.387 53.8181 102.24 50.0421 100.62 47.7454C98.6441 44.9444 95.1801 43.4498 91.7591 43.2344C88.3382 43.019 84.9409 43.9437 81.7471 45.1884C78.1184 46.6025 74.52 48.5569 72.287 51.7476C69.5713 55.6279 69.31 60.8446 70.6681 65.3819C71.4943 68.142 74.3139 71.5137 76.396 73.7315Z"
                fill="#2F2E41"
            />
            <path
                d="M170.501 259.931H0.945029C0.827 259.931 0.713798 259.884 0.630339 259.801C0.54688 259.717 0.5 259.604 0.5 259.486C0.5 259.368 0.54688 259.255 0.630339 259.171C0.713798 259.088 0.827 259.041 0.945029 259.041H170.501C170.619 259.041 170.732 259.088 170.816 259.171C170.899 259.255 170.946 259.368 170.946 259.486C170.946 259.604 170.899 259.717 170.816 259.801C170.732 259.884 170.619 259.931 170.501 259.931Z"
                fill="#CACACA"
            />
            <path
                d="M150.481 60.7246C150.088 60.725 149.709 60.5762 149.421 60.3082L142.231 53.6295C141.929 53.3483 141.75 52.9586 141.735 52.546C141.72 52.1335 141.87 51.7319 142.151 51.4294C142.432 51.1269 142.821 50.9483 143.234 50.9329C143.646 50.9174 144.048 51.0664 144.351 51.347L150.441 57.0042L162.491 44.9545C162.636 44.809 162.807 44.6934 162.997 44.6144C163.186 44.5354 163.389 44.4945 163.594 44.4941C163.799 44.4938 164.002 44.5339 164.191 44.6122C164.381 44.6905 164.553 44.8054 164.698 44.9505C164.843 45.0955 164.958 45.2677 165.036 45.4572C165.115 45.6467 165.155 45.8499 165.154 46.0549C165.154 46.26 165.113 46.4629 165.034 46.6521C164.955 46.8414 164.839 47.0131 164.694 47.1575L151.583 60.2685C151.438 60.4133 151.266 60.5281 151.077 60.6064C150.888 60.6846 150.686 60.7248 150.481 60.7246Z"
                fill="white"
            />
        </g>
        <defs>
            <clipPath id="clip0_1826_146698">
                <rect width="302.993" height="260" fill="white" transform="translate(0.5)" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'preivew'"
        :width="width"
        :height="height"
        :fill="bgfill"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M17.8655 18.4786C18.3234 18.4772 18.7676 18.6347 19.1224 18.9241C19.4772 19.2135 19.7206 19.6171 19.8112 20.0659C19.9018 20.5147 19.8339 20.9811 19.6192 21.3855C19.4044 21.7899 19.0561 22.1073 18.6335 22.2836C18.2109 22.4599 17.7403 22.4842 17.3018 22.3524C16.8633 22.2206 16.4841 21.9407 16.2288 21.5606C15.9735 21.1805 15.858 20.7236 15.9018 20.2679C15.9457 19.8121 16.1462 19.3856 16.4693 19.0612C16.6522 18.877 16.8698 18.7307 17.1094 18.6307C17.3489 18.5308 17.6059 18.4791 17.8655 18.4786ZM10.1931 21.8303H1.51857C1.11627 21.8278 0.731204 21.6667 0.446913 21.382C0.162621 21.0974 0.00203667 20.7121 0 20.3098L0 1.58791C0.00153445 1.18563 0.162019 0.800265 0.446474 0.51581C0.730929 0.231355 1.11629 0.0708704 1.51857 0.0693359L19.9511 0.0693359C20.3529 0.0706229 20.7377 0.231255 21.0211 0.515974L21.0716 0.572289C21.3221 0.851188 21.4605 1.213 21.46 1.58791V15.958C21.2658 15.8628 21.0716 15.7755 20.8774 15.6958C20.5952 15.5857 20.3074 15.4904 20.0152 15.4104V1.58791V1.55878L20.0016 1.54518C19.9907 1.53282 19.9754 1.52517 19.9589 1.52382H1.51857C1.50173 1.52431 1.48571 1.53122 1.4738 1.54313C1.46188 1.55505 1.45498 1.57106 1.45449 1.58791V20.3098C1.45683 20.326 1.46434 20.3409 1.47588 20.3525C1.48743 20.364 1.50241 20.3715 1.51857 20.3739H9.78332V20.4419V20.4593C9.76137 20.9516 9.90797 21.4365 10.1989 21.8342L10.1931 21.8303ZM11.8243 20.1797C12.1334 19.8048 12.4682 19.4518 12.8263 19.1233C14.2264 17.8494 15.9741 17.0125 17.6189 16.9872C19.2637 16.962 21.0949 17.7193 22.6776 19.0243C23.1126 19.383 23.5215 19.7724 23.901 20.1894C23.9607 20.2563 23.9954 20.3417 23.9993 20.4312C24.0032 20.5208 23.976 20.6089 23.9223 20.6807C23.5007 21.2798 22.9974 21.8171 22.4271 22.277C21.1745 23.277 19.5453 23.9256 18.0539 23.9353C16.5625 23.9451 14.9099 23.3139 13.5875 22.3818C12.916 21.9048 12.3152 21.3354 11.8029 20.6904C11.7456 20.6176 11.7163 20.5266 11.7202 20.434C11.7241 20.3414 11.761 20.2533 11.8243 20.1855V20.1797ZM13.3448 19.6923C13.0842 19.93 12.8372 20.1822 12.6049 20.4477C13.0245 20.9373 13.5025 21.3736 14.0283 21.7468C15.2206 22.5954 16.7237 23.178 18.0597 23.1644C19.3957 23.1508 20.8444 22.5605 21.9591 21.6711C22.3945 21.3203 22.7857 20.9179 23.1242 20.4729C22.8326 20.167 22.5233 19.8785 22.1979 19.6088C20.7628 18.4262 19.0909 17.7174 17.6403 17.7465C16.1897 17.7756 14.6128 18.5446 13.3525 19.6884L13.3448 19.6923ZM7.39283 14.3171H16.7858C16.8138 14.3181 16.8405 14.3296 16.8603 14.3494C16.8801 14.3693 16.8917 14.3959 16.8926 14.4239V15.1133C16.0153 15.209 15.159 15.4451 14.3565 15.8124L14.2516 15.8609H7.39283C7.36466 15.8604 7.33778 15.849 7.31786 15.8291C7.29794 15.8091 7.28653 15.7823 7.28603 15.7541V14.4239C7.28603 14.3956 7.29728 14.3684 7.31731 14.3484C7.33734 14.3283 7.3645 14.3171 7.39283 14.3171ZM5.46258 5.23092C5.63826 5.23053 5.81011 5.28231 5.95635 5.37967C6.10259 5.47704 6.21663 5.61562 6.28404 5.77786C6.35145 5.9401 6.36919 6.11869 6.33501 6.29102C6.30083 6.46335 6.21627 6.62166 6.09204 6.74589C5.96781 6.87012 5.8095 6.95468 5.63718 6.98886C5.46485 7.02304 5.28625 7.0053 5.12401 6.93789C4.96177 6.87048 4.82319 6.75644 4.72582 6.6102C4.62846 6.46396 4.57669 6.29211 4.57707 6.11643C4.57758 5.88173 4.67104 5.6568 4.837 5.49084C5.00295 5.32489 5.22788 5.23143 5.46258 5.23092ZM7.39283 5.34549H16.7858C16.814 5.34599 16.8409 5.3574 16.8608 5.37733C16.8807 5.39725 16.8921 5.42413 16.8926 5.4523V6.7825C16.8912 6.81037 16.8795 6.83673 16.8598 6.85646C16.8401 6.87619 16.8137 6.8879 16.7858 6.88931H7.39283C7.36481 6.88834 7.3382 6.87677 7.31838 6.85695C7.29856 6.83713 7.28699 6.81052 7.28603 6.7825V5.4523C7.28603 5.43827 7.28879 5.42438 7.29416 5.41142C7.29952 5.39847 7.30739 5.38669 7.31731 5.37677C7.32723 5.36686 7.339 5.35899 7.35196 5.35362C7.36492 5.34825 7.37881 5.34549 7.39283 5.34549ZM5.46258 9.71672C5.63818 9.71633 5.80995 9.76805 5.95615 9.86534C6.10234 9.96262 6.21639 10.1011 6.28386 10.2632C6.35133 10.4253 6.36918 10.6038 6.33515 10.7761C6.30113 10.9484 6.21676 11.1067 6.09273 11.231C5.96869 11.3553 5.81057 11.44 5.63837 11.4744C5.46617 11.5088 5.28763 11.4914 5.12536 11.4243C4.96308 11.3571 4.82437 11.2434 4.72677 11.0974C4.62917 10.9514 4.57707 10.7798 4.57707 10.6042C4.57707 10.3691 4.6703 10.1437 4.83631 9.97733C5.00232 9.81096 5.22755 9.71723 5.46258 9.71672ZM7.39283 9.83129H16.7858C16.814 9.83179 16.8409 9.8432 16.8608 9.86312C16.8807 9.88305 16.8921 9.90993 16.8926 9.9381V11.2683C16.8912 11.2962 16.8795 11.3225 16.8598 11.3423C16.8401 11.362 16.8137 11.3737 16.7858 11.3751H7.39283C7.36481 11.3741 7.3382 11.3626 7.31838 11.3428C7.29856 11.3229 7.28699 11.2963 7.28603 11.2683V9.9381C7.28603 9.90977 7.29728 9.8826 7.31731 9.86257C7.33734 9.84254 7.3645 9.83129 7.39283 9.83129ZM5.46258 14.2025C5.63818 14.2021 5.80995 14.2539 5.95615 14.3511C6.10234 14.4484 6.21639 14.5869 6.28386 14.749C6.35133 14.9111 6.36918 15.0896 6.33515 15.2619C6.30113 15.4342 6.21676 15.5925 6.09273 15.7168C5.96869 15.8411 5.81057 15.9258 5.63837 15.9602C5.46617 15.9946 5.28763 15.9772 5.12536 15.91C4.96308 15.8429 4.82437 15.7292 4.72677 15.5832C4.62917 15.4372 4.57707 15.2656 4.57707 15.09C4.57758 14.8551 4.67098 14.63 4.83688 14.4637C5.00277 14.2974 5.22771 14.2035 5.46258 14.2025ZM17.3082 18.8631C17.4596 18.8627 17.6077 18.9071 17.7338 18.9908C17.86 19.0744 17.9586 19.1935 18.0173 19.3331C18.0759 19.4726 18.0919 19.6264 18.0633 19.7751C18.0347 19.9237 17.9628 20.0606 17.8565 20.1684C17.7503 20.2763 17.6146 20.3503 17.4664 20.3811C17.3182 20.4119 17.1641 20.3982 17.0237 20.3417C16.8833 20.2852 16.7627 20.1884 16.6772 20.0635C16.5917 19.9386 16.545 19.7912 16.5431 19.6399C16.5431 19.4358 16.6233 19.24 16.7665 19.0947C16.9096 18.9493 17.1042 18.8661 17.3082 18.8631Z"
            fill="#6B7280"
        />
    </svg>

    <svg
        v-else-if="name == 'download_arrow_up'"
        :width="width"
        :height="height"
        :fill="bgfill"
        viewBox="0 0 17 16"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M4 2C3.72386 2 3.5 2.22386 3.5 2.5C3.5 2.77614 3.72386 3 4 3H13C13.2761 3 13.5 2.77614 13.5 2.5C13.5 2.22386 13.2761 2 13 2H4ZM8.85355 4.14645C8.65829 3.95118 8.34171 3.95118 8.14645 4.14645L4.64645 7.64645C4.45118 7.84171 4.45118 8.15829 4.64645 8.35355C4.84171 8.54882 5.15829 8.54882 5.35355 8.35355L8 5.70711V13.5C8 13.7761 8.22386 14 8.5 14C8.77614 14 9 13.7761 9 13.5V5.70711L11.6464 8.35355C11.8417 8.54882 12.1583 8.54882 12.3536 8.35355C12.5488 8.15829 12.5488 7.84171 12.3536 7.64645L8.85355 4.14645Z"
            fill="#9CA3AF"
        />
    </svg>
    <svg
        v-else-if="name == 'invalid_file_type'"
        :width="width"
        :height="height"
        viewBox="0 0 64 51"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <g clip-path="url(#clip0_2182_116896)">
            <path
                d="M3.5196 11.0896V6.8509C3.5196 5.62652 4.5126 4.6334 5.73698 4.6334H7.0016V2.60715C7.0016 1.38277 7.9946 0.389648 9.21898 0.389648H29.5575C30.7819 0.389648 31.7749 1.38277 31.7749 2.60715V4.6334H58.2407C59.465 4.6334 60.4581 5.62652 60.4581 6.8509V11.0896H61.7826C63.007 11.0896 64 12.0826 64 13.307C64 13.4695 63.9824 13.628 63.9489 13.7809L59.5752 45.7144C59.2279 48.2741 57.3041 50.4119 54.6082 50.4119H8.3856C5.6721 50.4119 3.68048 48.259 3.40773 45.6423L0.0116032 13.532C-0.112397 12.3173 0.771603 11.2313 1.98635 11.1071L2.21148 11.0955V11.0896H3.5196Z"
                fill="#65728C"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M5.73438 11.0893H58.2381V6.85019H29.5549V2.60645H9.21637V6.85019H5.73438V11.0893Z"
                fill="#BAE7FF"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M61.7821 13.3066L57.3837 45.4194C57.1783 46.9279 56.1304 48.1941 54.6077 48.1941H8.38506C6.86019 48.1941 5.76619 46.9363 5.60694 45.4169L2.21094 13.3066H61.7821Z"
                fill="white"
            />
            <g clip-path="url(#clip1_2182_116896)">
                <path
                    d="M31.9961 20.7383C34.7597 20.7383 37.264 21.8597 39.0768 23.6691C40.8862 25.482 42.0076 27.9863 42.0076 30.7498C42.0076 33.5137 40.8861 36.0177 39.0768 37.8306C37.2641 39.6403 34.7597 40.7616 31.9961 40.7616C29.2324 40.7616 26.728 39.6403 24.9152 37.8307C23.1058 36.0179 21.9844 33.5137 21.9844 30.7498C21.9844 27.9863 23.1058 25.482 24.9152 23.6691C26.7282 21.8597 29.2324 20.7383 31.9961 20.7383ZM33.9535 27.1181C34.4016 26.6632 35.1301 26.6611 35.5807 27.1139C36.0311 27.5663 36.0329 28.3021 35.5851 28.7569L33.6191 30.7508L35.587 32.747C36.0316 33.1983 36.0252 33.9293 35.5739 34.3799C35.1221 34.8306 34.3959 34.8293 33.9518 34.3779L31.9969 32.396L30.0387 34.382C29.5906 34.8371 28.8622 34.8391 28.4117 34.3861C27.9614 33.9339 27.9593 33.198 28.4074 32.7432L30.3731 30.7492L28.405 28.753C27.9608 28.3018 27.9668 27.5707 28.4183 27.1199C28.87 26.6694 29.5963 26.6705 30.0404 27.1221L31.9953 29.1042L33.9535 27.1181ZM37.7742 24.9718C36.2957 23.4933 34.253 22.579 31.9961 22.579C29.7391 22.579 27.6963 23.4933 26.2178 24.9719C24.7394 26.4504 23.8251 28.4931 23.8251 30.75C23.8251 33.0069 24.7394 35.0498 26.2178 36.5281C27.6963 38.0067 29.7391 38.9212 31.9961 38.9212C34.253 38.9212 36.2957 38.0069 37.7742 36.5281C39.2526 35.0498 40.167 33.0069 40.167 30.75C40.167 28.4931 39.2528 26.4504 37.7742 24.9718Z"
                    fill="#EF4444"
                />
            </g>
        </g>
        <defs>
            <clipPath id="clip0_2182_116896">
                <rect width="64" height="50.0223" fill="white" transform="translate(0 0.389648)" />
            </clipPath>
            <clipPath id="clip1_2182_116896">
                <rect
                    width="20.0234"
                    height="20.0233"
                    fill="white"
                    transform="translate(21.9844 20.7383)"
                />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'circle'"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
    >
        <path
            fill="currentColor"
            d="M12 3.5a8.5 8.5 0 1 0 0 17a8.5 8.5 0 0 0 0-17M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12"
        />
    </svg>
    <svg
        v-else-if="name == 'circle-fill'"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
    >
        <path
            fill="currentColor"
            d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12"
        />
    </svg>
    <svg
        v-else-if="name == 'rect'"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
    >
        <path
            fill="currentColor"
            d="M2 7.25A3.25 3.25 0 0 1 5.25 4h13.5A3.25 3.25 0 0 1 22 7.25v9.5A3.25 3.25 0 0 1 18.75 20H5.25A3.25 3.25 0 0 1 2 16.75zM5.25 5.5A1.75 1.75 0 0 0 3.5 7.25v9.5c0 .966.784 1.75 1.75 1.75h13.5a1.75 1.75 0 0 0 1.75-1.75v-9.5a1.75 1.75 0 0 0-1.75-1.75z"
        />
    </svg>
    <svg
        v-else-if="name == 'rect-fill'"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
    >
        <path
            fill="currentColor"
            d="M5.25 4A3.25 3.25 0 0 0 2 7.25v9.5A3.25 3.25 0 0 0 5.25 20h13.5A3.25 3.25 0 0 0 22 16.75v-9.5A3.25 3.25 0 0 0 18.75 4z"
        />
    </svg>
    <svg
        v-else-if="name == 'line' || name == 'line-vert'"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
    >
        <path
            fill="currentColor"
            d="M21.707 2.293a1 1 0 0 1 0 1.414l-18 18a1 1 0 0 1-1.414-1.414l18-18a1 1 0 0 1 1.414 0"
        />
    </svg>
    <svg
        v-else-if="name == 'ellipse-fill'"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
    >
        <path fill="currentColor" d="M2 12a8 8 0 0 1 8-8h4a8 8 0 1 1 0 16h-4a8 8 0 0 1-8-8" />
    </svg>
    <svg
        v-else-if="name == 'ellipse'"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
    >
        <path
            fill="currentColor"
            d="M2 12a8 8 0 0 1 8-8h4a8 8 0 1 1 0 16h-4a8 8 0 0 1-8-8m8-6.5a6.5 6.5 0 0 0 0 13h4a6.5 6.5 0 1 0 0-13z"
        />
    </svg>
    <svg
        v-else-if="name == 'coursebook'"
        :width="width"
        :height="height"
        viewBox="0 0 340 340"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <g clip-path="url(#clip0_5858_256924)">
            <path
                d="M168.406 255L27.625 178.5V185.406L168.406 261.641L325.922 178.234V171.328L168.406 255ZM168.406 213.297L34.5313 143.969V150.875L168.406 220.203L325.922 136.797V130.156L168.406 213.297ZM168.406 268.812L27.625 192.578V199.484L168.406 275.719L325.922 192.578V185.672L168.406 268.812ZM168.406 199.484L34.5313 130.156V137.062L168.406 206.391L325.922 122.984V116.078L168.406 199.484Z"
                fill="#C9D4FF"
            />
            <path
                d="M27.6258 157.784C7.70394 145.3 21.2508 126.175 27.6258 122.987C37.7196 118.206 166.548 192.315 166.548 192.315L340.001 102.003C340.001 102.003 198.423 44.0964 187.266 39.5808C176.11 34.7995 176.376 35.0652 166.548 39.5808C130.688 56.3152 45.4227 95.0964 20.7196 109.175C-8.49919 125.643 4.25081 157.518 13.2821 164.425C11.9539 164.69 10.8914 165.487 9.82894 166.815C-4.78044 184.346 -0.796063 208.253 6.90706 213.565C17.2664 220.737 166.548 303.878 166.548 303.878L340.001 213.565V199.753L166.548 290.065C166.548 290.065 40.6414 219.143 20.7196 206.659C2.65706 194.971 17.7977 173.721 25.5008 171.862C61.8914 192.315 166.282 248.096 166.282 248.096L339.735 157.784V143.971L166.282 234.284C166.548 234.284 47.5477 170.268 27.6258 157.784ZM180.36 60.5652L284.485 102.268L235.876 122.987L131.751 81.5495L180.36 60.5652Z"
                fill="#1890FF"
            />
            <path
                d="M180.362 60.466L284.706 102.095L235.905 123.006L131.758 81.5199L180.362 60.466Z"
                fill="white"
            />
        </g>
        <defs>
            <clipPath id="clip0_5858_256924">
                <rect width="340" height="340" fill="white" />
            </clipPath>
        </defs>
    </svg>
    <svg
        v-else-if="name == 'desktop'"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        :width="width"
        :height="height"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
    >
        <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25"
        ></path>
    </svg>
    <svg
        v-else-if="name == 'list'"
        version="1.1"
        id="Layer_1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        x="0px"
        y="0px"
        viewBox="0 0 122.88 95.95"
        style="enable-background: new 0 0 122.88 95.95"
        xml:space="preserve"
    >
        <g>
            <path
                class="st0"
                d="M8.94,0h105c4.92,0,8.94,4.02,8.94,8.94l0,0c0,4.92-4.02,8.94-8.94,8.94h-105C4.02,17.88,0,13.86,0,8.94l0,0 C0,4.02,4.02,0,8.94,0L8.94,0z M8.94,78.07h105c4.92,0,8.94,4.02,8.94,8.94l0,0c0,4.92-4.02,8.94-8.94,8.94h-105 C4.02,95.95,0,91.93,0,87.01l0,0C0,82.09,4.02,78.07,8.94,78.07L8.94,78.07z M8.94,39.03h105c4.92,0,8.94,4.02,8.94,8.94l0,0 c0,4.92-4.02,8.94-8.94,8.94h-105C4.02,56.91,0,52.89,0,47.97l0,0C0,43.06,4.02,39.03,8.94,39.03L8.94,39.03z"
            />
        </g>
    </svg>
    <svg
        v-else-if="name == 'link'"
        :width="width"
        :height="height"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
        viewBox="0 0 16 16"
    >
        <path
            d="M9.49999 4H10.5C12.433 4 14 5.567 14 7.5C14 9.36856 12.5357 10.8951 10.6941 10.9948L10.5023 11L9.5023 11.0046C9.22616 11.0059 9.00127 10.783 8.99999 10.5069C8.99888 10.2614 9.17481 10.0565 9.40787 10.0131L9.4977 10.0046L10.5 10C11.8807 10 13 8.88071 13 7.5C13 6.17452 11.9685 5.08996 10.6644 5.00532L10.5 5H9.49999C9.22386 5 8.99999 4.77614 8.99999 4.5C8.99999 4.25454 9.17687 4.05039 9.41012 4.00806L9.49999 4H10.5H9.49999ZM5.5 4H6.5C6.77614 4 7 4.22386 7 4.5C7 4.74546 6.82312 4.94961 6.58988 4.99194L6.5 5H5.5C4.11929 5 3 6.11929 3 7.5C3 8.82548 4.03154 9.91004 5.33562 9.99468L5.5 10H6.5C6.77614 10 7 10.2239 7 10.5C7 10.7455 6.82312 10.9496 6.58988 10.9919L6.5 11H5.5C3.567 11 2 9.433 2 7.5C2 5.63144 3.46428 4.10487 5.30796 4.00518L5.5 4H6.5H5.5ZM5.50023 7L10.5002 7.0023C10.7764 7.00242 11.0001 7.22638 11 7.50252C10.9999 7.74798 10.8229 7.95205 10.5897 7.99428L10.4998 8.0023L5.49977 8C5.22363 7.99987 4.99987 7.77591 5 7.49977C5.00011 7.25431 5.17708 7.05024 5.41035 7.00801L5.50023 7Z"
            :fill="fill"
        />
    </svg>
    <svg
        v-else-if="name == 'pen'"
        :width="width"
        :height="height"
        :fill="bgfill"
        :xmlns="xmlns"
        :class="className"
        viewBox="0 0 24 24"
    >
        <path
            :fill="fill"
            d="M20.952 3.047a3.58 3.58 0 0 0-5.06 0L3.94 15a3.1 3.1 0 0 0-.825 1.476L2.02 21.077a.75.75 0 0 0 .904.903l4.601-1.096a3.1 3.1 0 0 0 1.477-.825L19 10.06a1.75 1.75 0 0 1-.006 2.47l-1.783 1.784a.75.75 0 1 0 1.06 1.06l1.784-1.783A3.25 3.25 0 0 0 20.06 9l.892-.892a3.58 3.58 0 0 0 0-5.06m-4 1.06a2.078 2.078 0 1 1 2.94 2.94L7.941 18.999c-.211.21-.475.357-.764.426l-3.416.813l.813-3.415c.069-.29.217-.554.427-.764z"
        />
    </svg>

    <svg
        v-else-if="name == 'edit-pen'"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-6"
    >
        <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
        />
    </svg>
    <svg
        v-else-if="name == 'trash'"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="{1.5}"
        stroke="currentColor"
        class="size-6"
    >
        <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
        />
    </svg>
</template>
<script>
export default {
    props: {
        name: { default: null },
        bgfill: { default: 'none' },
        fill: { default: '#9CA3AF' },
        darkfill: { default: '#6B7280' },
        viewbox: { default: '0 0 20 20' },
        width: { default: 20 },
        height: { default: 20 },
        stroke: { default: 'white' },
        className: { default: null },
    },
    data: {
        xmlns: 'http://www.w3.org/2000/svg',
    },
};
</script>
