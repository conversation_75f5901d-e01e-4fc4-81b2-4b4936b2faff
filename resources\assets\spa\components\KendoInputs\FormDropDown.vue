<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :editor-id="id"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
            :class="labelClass"
        >
            {{ label }}
            <span v-if="indicaterequired || required" :class="'ml-1 text-red-500'">*</span>
            <i class="block text-[8px] text-gray-500" v-if="helperText"> [{{ helperText }}] </i>
        </klabel>
        <div :class="wrapClass">
            <dropdown
                :disabled="disabled"
                :style="{ width: '100%' }"
                :data-items="dropDownDataItem"
                :default-item="dropDownDefaultItem"
                :text-field="textField"
                :data-item-key="dataItemKey"
                :value="selectedValue"
                :filterable="true"
                :value-field="valueField"
                :value-primitive="valuePrimitive"
                @filterchange="filterChange"
                :loading="loading"
                @change="onChange"
                @close="handleClose"
                @input="handleChange"
                @blur="handleBlur"
                @focus="handleFocus"
                :popup-settings="popupOptions"
                :class="fieldClass"
                :footer="'myFooter'"
                :name="name"
            >
                <template v-slot:myFooter="{ props }">
                    <span v-if="hasFooter" :class="getFooterClass" @click="handleFooterClick">{{
                        footerText
                    }}</span>
                </template>
            </dropdown>
            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else>{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { filterBy } from '@progress/kendo-data-query';
import { twMerge } from 'tailwind-merge';
import { nextTick } from 'vue';

export default {
    props: {
        modelValue: [String, Object, Number],
        optional: Boolean,
        disabled: Boolean,
        placeholder: String,
        touched: Boolean,
        label: String,
        name: String,
        value: [String, Object, Number, Boolean],
        className: String,
        textField: String,
        valueField: {
            type: String,
            default: 'id',
        },
        valuePrimitive: {
            type: Boolean,
            default: false,
        },
        indicaterequired: { type: Boolean, default: false },
        required: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
        dataItemKey: String,
        validationMessage: String,
        hint: String,
        helperText: String,
        id: String,
        valid: Boolean,
        dataItems: Array,
        defaultItem: Object,
        pt: {
            type: Object,
            default: {},
        },
        valueRender: String,
        footerText: String,
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        dropdown: DropDownList,
    },
    data() {
        return {
            dropDownDataItem: this.dataItems,
            dropDownDefaultItem: this.defaultItem,
            loading: false,
            isSearch: false,
            popupOptions: {
                popupClass: 'tw-popup',
                animate: false,
            },
            skipValidation: false,
        };
    },
    computed: {
        hasFooter() {
            return this.footerText != '' && this.footerText != null;
        },
        showValidationMessage() {
            console.log(
                'this',
                this.skipValidation,
                this.$props.touched,
                this.$props.validationMessage
            );
            return !this.skipValidation && this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        selectedValue() {
            return this.value;
        },
        rootClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('tw-form__fieldwrapper field-horizontal', this.pt.root);
            }
            return twMerge('tw-form__fieldwrapper', this.pt.root);
        },
        wrapClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('k-form-field-wrap', this.pt.wrap);
            }
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('tw-form__label mb-1 leading-5 text-gray-700', this.pt.label);
            }
            return twMerge('tw-form__label mb-1 leading-5 text-gray-700', this.pt.label);
        },
        inputClass() {
            return twMerge('tw-form__input', this.pt.input);
        },
        fieldClass() {
            return twMerge('tw-input__dropdown', this.className, this.pt.field);
        },
        getFooterClass() {
            return twMerge(
                'text-center block text-primary-blue-500 cursor-pointer',
                this.pt.footer
            );
        },
    },
    emits: {
        change: null,
        blur: null,
        focus: null,
        customEvent: null,
        'update:modelValue': null,
    },
    methods: {
        filterChange(event) {
            if (event.event.target.value != '') {
                this.loading = true;
                this.isSearch = true;
                this.dropDownDataItem = this.filterData(event.filter);
                // this.dropDownDefaultItem = this.dropDownDataItem.shift();
                this.loading = false;
            } else {
                // this.$emit('change', event)
                this.dropDownDefaultItem = this.defaultItem;
                this.dropDownDataItem = this.filterData(event.filter);
                this.isSearch = false;
            }
        },
        filterData(filter) {
            const filterData = this.dataItems;
            return filterBy(filterData, filter);
        },
        handleChange(e) {
            this.$emit('input', e.target.value);
            //console.log("Input event");
            //this.$emit("change", e);
        },
        handleBlur(e) {
            // this.skipValidation = false;
            this.$emit('blur', e);
        },
        handleClose(e) {
            this.skipValidation = false;
            if (this.dropDownDefaultItem != e.target.value || this.isSearch) {
                this.$emit('change', e);
            }
        },
        handleFocus(e) {
            this.skipValidation = true;
            this.$emit('focus', e);
        },
        onChange(event) {
            //console.log("Change event");
            this.$emit('customEvent', event);
            this.$emit('update:modelValue', event.value);
            //this.$emit("change", event);
        },
        handleFooterClick(event) {
            // Manually call handleClose
            this.$emit('footerevent', event);
            setTimeout(() => {
                const closeEvent = {
                    target: { value: this.selectedValue },
                };
                this.handleClose(closeEvent);
            }, 0);
        },
    },
    watch: {
        dataItems: function () {
            this.dropDownDataItem = this.dataItems;
        },
        defaultItem: function () {
            this.defaultItemInner = this.defaultItem;
        },
    },
};
</script>

<style>
.k-invalid-msg {
    display: none;
}

.k-invalid {
    border-color: red !important;
}
</style>
