<template>
    <fieldwrapper :class="rootClasses">
        <klabel
            :class="labelClasses"
            :editor-id="id"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
        >
            {{ label }}
        </klabel>
        <div :class="wrapperClass">
            <template v-if="valueLabel && labelPosition == 'start'">
                <span :class="valueClass">{{ value ? dynamicLabel[0] : dynamicLabel[1] }}</span>
            </template>
            <kswitch
                :valid="valid"
                :id="id"
                :checked="value"
                :disabled="disabled"
                :required="required"
                :placeholder="placeholder"
                :default-checked="defaultChecked"
                :default-value="defaultValue"
                @change="handleChange"
                @blur="handleBlur"
                @focus="handleFocus"
                :class="fieldClasses"
            />
            <template v-if="valueLabel && labelPosition == 'end'">
                <span :class="valueClass">{{ value ? dynamicLabel[0] : dynamicLabel[1] }}</span>
            </template>
            <span v-if="showValidationMessage && showHint">
                <error>
                    {{ validationMessage }}
                </error>
                <hint>{{ hint }}</hint>
            </span>
            <error v-else-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else-if="showHint">{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { Switch } from '@progress/kendo-vue-inputs';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: [String, Number],
        optional: Boolean,
        disabled: Boolean,
        placeholder: String,
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        id: String,
        valid: Boolean,
        className: {
            type: String,
            default: '',
        },
        value: {
            type: Boolean,
            default: false,
        },
        required: {
            type: Boolean,
            default: false,
        },
        pt: {
            type: Object,
            default: {},
        },
        valueLabel: {
            type: Boolean,
            default: false,
        },
        labelPosition: {
            type: String,
            default: 'start', //start/end
        },
        dynamicLabel: {
            type: Array,
            default: () => ['Yes', 'No'],
        },
        defaultChecked: {
            type: Boolean,
            default: false,
        },
        defaultValue: [Boolean, String, Number],
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        kswitch: Switch,
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        labelClasses() {
            return twMerge('k-form-label', this.pt.label);
        },
        fieldClasses() {
            return twMerge('tw-input__checkbox', this.className, this.pt.field);
        },
        rootClasses() {
            return twMerge('tw-form__wrapper', this.pt.root);
        },
        wrapperClass() {
            return twMerge(
                'k-form-field-wrap',
                this.valueLabel && '!flex items-end justify-between',
                this.pt.wrapper
            );
        },
        valueClass() {
            return twMerge('text-xs text-gray-500 font-normal', this.pt.value);
        },
    },
    emits: {
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    methods: {
        handleChange(e) {
            console.log('eee', e);
            this.$emit('change', e);
            this.$emit('update:modelValue', e.value);
        },
        handleBlur(e) {
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
