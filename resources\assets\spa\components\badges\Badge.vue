<template>
    <div v-bind="$attrs" :class="rootClasses">
        <slot />
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { twMerge } from 'tailwind-merge';
import { statusColors, colorMap } from './statusVariants';

// Define interfaces for type safety
interface ColorConfig {
    bg: string;
    text: string;
    darkBg?: string;
    darkText?: string;
}

interface ColorMap {
    [key: string]: ColorConfig | string;
}

interface BorderRadiusMap {
    [key: string]: string;
}

export default defineComponent({
    name: 'Badge',
    props: {
        pt: {
            type: Object,
            default: () => ({}),
        },
        variant: {
            type: String,
            default: 'default',
            validator: (value: string) => {
                const validVariants = [
                    'info',
                    'primary',
                    'primary-100',
                    'secondary',
                    'warning',
                    'error',
                    'success',
                    'paid',
                    'partially paid',
                    'purple',
                    'Agent Apply',
                    'Deactivating',
                    'Cancelled',
                    'cancelled',
                    'Completed',
                    'Converted',
                    'Saved',
                    'Current Student',
                    'Deferred',
                    'Did Not Commence',
                    'Enrolled',
                    'Expired Offer',
                    'Expired',
                    'Finished',
                    'Graduated',
                    'New Application Request',
                    'New Course Request',
                    'Offered',
                    'Placed',
                    'Reported',
                    'Withdrawn',
                    'Transferred',
                    'Transitioned',
                    'Suspended',
                    'Informed',
                    'Not resolved',
                    'Onboarding',
                    'Activated',
                ];
                return validVariants.includes(value);
            },
        },
        shape: {
            type: String,
            default: 'md',
            validator: (value: string) => ['sm', 'md', 'lg', 'full'].includes(value),
        },
        dark: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        rootClasses(): string {
            // Border radius configurations
            const borderRadiusMap: BorderRadiusMap = {
                sm: 'rounded-sm',
                md: 'rounded-md',
                lg: 'rounded-lg',
                full: 'rounded-full',
            };

            // Default values
            const defaultColorClass = this.dark
                ? 'bg-gray-700 text-white'
                : 'bg-gray-100 text-gray-800';
            const defaultBorderRadius = borderRadiusMap['md'];

            // Resolve color classes
            const colorClass = (() => {
                const mappedColor = colorMap[this.variant];
                if (typeof mappedColor === 'string') return mappedColor;
                if (mappedColor) {
                    return this.dark
                        ? `${mappedColor.darkBg || mappedColor.bg} ${mappedColor.darkText || 'text-white'}`
                        : `${mappedColor.bg} ${mappedColor.text}`;
                }
                return defaultColorClass;
            })();

            // Resolve border radius
            const borderRadiusClass = borderRadiusMap[this.shape] || defaultBorderRadius;

            // Combine classes
            return twMerge(
                'tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs capitalize',
                colorClass,
                borderRadiusClass,
                this.pt?.root || ''
            );
        },
    },
});
</script>
