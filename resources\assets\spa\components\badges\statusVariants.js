export const statusColors = {
    green: {
        bg: 'bg-green-100',
        text: 'text-green-800',
        darkBg: 'bg-green-700',
        darkText: 'text-white',
    },
    red: { bg: 'bg-red-100', text: 'text-red-800', darkBg: 'bg-red-700', darkText: 'text-white' },
    blue: {
        bg: 'bg-primary-blue-50',
        text: 'text-primary-blue-500',
        darkBg: 'bg-primary-blue-700',
        darkText: 'text-white',
    },
    blue_100: {
        bg: 'bg-primary-blue-100',
        text: 'text-primary-blue-800',
        darkBg: 'bg-primary-blue-700',
        darkText: 'text-white',
    },
    yellow: {
        bg: 'bg-yellow-100',
        text: 'text-yellow-800',
        darkBg: 'bg-yellow-700',
        darkText: 'text-white',
    },
    gray: {
        bg: 'bg-gray-100',
        text: 'text-gray-800',
        darkBg: 'bg-gray-700',
        darkText: 'text-white',
    },
    orange: {
        bg: 'bg-orange-100',
        text: 'text-orange-800',
        darkBg: 'bg-orange-700',
        darkText: 'text-white',
    },
    purple: {
        bg: 'bg-purple-100',
        text: 'text-purple-800',
        darkBg: 'bg-purple-700',
        darkText: 'text-white',
    },
};

export const colorMap = {
    // Base variants
    info: statusColors.blue,
    primary: statusColors.blue,
    'primary-100': statusColors.blue_100,
    secondary: statusColors.gray,
    warning: statusColors.yellow,
    error: statusColors.red,
    success: statusColors.green,
    paid: statusColors.green,
    'partially paid': statusColors.yellow,
    purple: statusColors.purple,

    // Status variants
    'Agent Apply': statusColors.blue,
    Deactivating: statusColors.red,
    Cancelled: statusColors.red,
    cancelled: 'bg-red-500 text-white',
    Completed: statusColors.green,
    Converted: statusColors.yellow,
    Saved: statusColors.yellow,
    'Current Student': statusColors.blue,
    Deferred: statusColors.orange,
    'Did Not Commence': 'bg-gray-500 text-white',
    Enrolled: statusColors.blue,
    'Expired Offer': 'bg-gray-400 text-white',
    Expired: 'bg-gray-200 text-gray-800',
    Finished: 'bg-green-600 text-white',
    Graduated: 'bg-teal-500 text-white',
    'New Application Request': 'bg-indigo-300 text-indigo-700',
    'New Course Request': 'bg-indigo-400 text-indigo-800',
    Offered: 'bg-yellow-400 text-yellow-800',
    Placed: 'bg-green-300 text-green-700',
    Reported: 'bg-purple-400 text-purple-800',
    Withdrawn: 'bg-red-400 text-white',
    Transferred: 'bg-blue-300 text-blue-700',
    Transitioned: 'bg-yellow-500 text-yellow-900',
    Suspended: 'bg-red-600 text-white',
    Informed: statusColors.blue,
    'Not resolved': statusColors.red,
    Onboarding: statusColors.blue_100,
    Activated: statusColors.green,
};
