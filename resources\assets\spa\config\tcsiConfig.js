export const tcsiConfig = {
    gender: {
        F: 'Female',
        M: 'Male',
        X: 'Indeterminate/Intersex/Unspecified',
    },
    reportingType: {
        hep: 'HEP',
        vsl: 'VSL',
        pir: 'PIR',
    },
    submissionTypeWithReportingType: {
        hep: {
            1: 'Course of Study',
            2: 'Course',
            3: 'Campus',
            4: 'Course on Campus',
            5: 'Student',
            13: 'Revise first reported addresses',
            14: 'Commonwealth Scholarships',
            6: 'Course Admission',
            7: 'Course Admission HDR',
            15: 'Exit Award',
            16: 'Aggregate awards',
            8: 'Unit Enrolment',
            17: 'Unit Enrolments (AOU)',
            9: 'SA-HELP Loan',
            10: 'OS-HELP Loan',
            11: 'Staff-Full Time',
            12: 'Casual Staff Actuals',
            18: 'Casual Staff Estimates',
            19: 'Course Application',
            20: 'Course Preferences',
            21: 'Course Offers',
        },
        vsl: {
            2: 'Courses',
            3: 'Delivery Locations',
            5: 'Student',
            13: 'Revise First Reported Adresses',
            6: 'Course Admissions',
            8: 'Unit Enrolments',
        },
        pir: {
            1: 'Course of Study',
            2: 'Course',
            3: 'Campus',
            4: 'Course on Campus',
            5: 'Student',
            6: 'Course Admission',
            7: 'Course Admission HDR',
            8: 'Unit Enrolment',
            11: 'Staff-Full Time',
            12: 'Casual Staff Actuals',
            18: 'Casual Staff Estimates',
        },
    },
    submissionType: {
        1: 'Course of Study',
        2: 'Course',
        3: 'Campus',
        4: 'Course on Campus',
        5: 'Student',
        6: 'Course Admission',
        7: 'Course Admission HDR',
        8: 'Unit Enrolment',
        9: 'SA-HELP Loan',
        10: 'OS-HELP Loan',
        11: 'Staff-Full Time',
        12: 'Staff Casual Actual',
    },
    reportingYearType: {
        '': '- - Select Type - -',
        full: 'Full Year',
        half: 'Half Year',
    },
    highest_qualification_code: {
        6: 'Doctorate by research or coursework',
        7: "Master's by research, coursework or Master's extended",
        5: 'Other Postgraduate (including Postgraduate Qual or Prelim; Grad Dip/Postgrad Dip New/Extended and Graduate Certificate)',
        2: "Bachelor's (including Bachelor's graduate entry, Bachelor's honours and Bachelor's pass)",
        3: 'Other',
        4: 'No information',
    },
    highest_qualification_place_code: {
        1: 'The Australian higher education provider in which they are now working or one of its controlled entities',
        2: 'Another approved Australian higher education provider or entity',
        3: 'Other Australian education higher education provider or entity',
        4: 'An overseas institution or entity',
        5: 'No information',
    },
    work_contract_code: {
        1: 'Full-time work contract',
        2: 'Fractional full-time work contract',
    },
    staff_work_level_code: {
        '001': 'Vice Chancellor',
        '005': 'Deputy Vice-Chancellor',
        '013': 'Level E',
        '014': 'Level D',
        '042': 'Level C',
        '066': 'Level B',
        100: 'Level A',
        200: 'Non-academic staff outside award; generally junior, trainee or apprentice staff whose remuneration package is below level 1',
        220: 'Non-academic staff outside award; generally senior executives whose remuneration package exceeds level 10',
        201: 'Level 1',
        202: 'Level 2',
        203: 'Level 3',
        204: 'Level 4',
        205: 'Level 5',
        206: 'Level 6',
        207: 'Level 7',
        208: 'Level 8',
        209: 'Level 9',
        210: 'Level 10',
        999: 'No Information',
    },
    organisational_unit_code: {
        2100: 'Libraries',
        2200: 'Computing centres',
        2300: 'Educational research and development centres',
        2400: 'External studies centres (excluding academic functions)',
        2500: 'Audio-visual and media centres',
        2900: 'Other academic support services',
        3900: 'Student services (incl. health services, counselling and accommodation services and student residences, employment services, student loans/scholarships/assistance services and other student services',
        4900: 'Public services (incl. adult education, continuing education, public broadcasting services and other public services)',
        5100: 'Administration and overhead services',
        5200: 'Buildings, plant and grounds',
        5300: 'Cleaning services',
        5400: 'Security and caretaker services',
        5900: 'Other general Higher Education providers services',
        5910: 'CRC (Cooperative Research Centres)',
        6100: 'Computing services',
        6200: 'Research, development, testing or consultancy services',
        6900: 'Other independent operations which are controlled entities',
        999999: 'No information',
    },
    work_sector_code: {
        1: 'Higher Education work sector.',
        2: 'TAFE work sector',
        9: 'No information',
    },
    function_code: {
        1: 'Teaching only function',
        2: 'Research only function',
        3: 'Teaching-and-research function',
        4: 'Other function',
        999: 'No information',
    },
    TcsiStatusArr: {
        all: 'All',
        processed: 'Processed',
        not_processed: 'Not Processed',
    },
    aboriginal_and_torres_strait_islander: {
        2: 'Non indigenous – neither Aboriginal nor Torres Strait Islander origin',
        3: 'Of Aboriginal origin but not Torres Strait Islander',
        4: 'Of Torres Strait Islander origin but not Aboriginal',
        5: 'Both Aboriginal and Torres Strait Islander origin',
        9: 'No information',
    },
    classificationGroups: [
        { min: 1, max: 41, code: '01' },
        { min: 42, max: 65, code: '02' },
        { min: 66, max: 99, code: '03' },
        { min: 100, max: 128, code: '04' },
        { min: 200, max: 220, code: '11' },
    ],
};
