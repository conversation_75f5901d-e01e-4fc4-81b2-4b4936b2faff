<template>
    <AsyncForm
        :form-config="formFields"
        :initial-values="store.formData"
        @submit="onSubmit"
        @change="onChange"
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :dialogTitle="'Generate Pay Period'"
        :override="true"
        :store="store"
    >
        <PayPeriodFormContent />
    </AsyncForm>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { usePayPeriodStore } from '@spa/stores/modules/pay-period/payPeriodStore.js';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import PayPeriodFormContent from '@spa/modules/pay-period/partials/PayPeriodFormContent.vue';

const store = usePayPeriodStore();

const initialFormValues = ref({
    pay_period_type: '',
    pay_period_frequency: '',
    start_date_from: '',
    start_date_to: '',
});

// Form field configuration
const formFields = computed(() => ({
    pay_period_type: {
        type: 'dropdown',
        label: 'Pay Period Type',
        options: store.payPeriodTypes || [],
        validator: requiredtrue,
        colSpan: 1,
    },
    pay_period_frequency: {
        type: 'dropdown',
        label: 'Pay Period Frequency',
        options: store.payPeriodFrequencies || [],
        validator: requiredtrue,
        colSpan: 1,
    },
    start_date_from: {
        type: 'date',
        label: 'Start Date From',
        format: 'dd-MM-yyyy',
        validator: requiredtrue,
        colSpan: 1,
    },
    start_date_to: {
        type: 'date',
        label: 'Start Date To',
        format: 'dd-MM-yyyy',
        validator: requiredtrue,
        colSpan: 1,
    },
}));

const onSubmit = (data) => {
    store.update(data);
};

const onChange = (data) => {
    // Handle form changes if needed
};

// Watch for form dialog opening to fetch constants
watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            store.getFormConstants();
        }
    }
);

// Watch for form constants to update dropdown options
watch(
    () => store.payPeriodTypes,
    (val) => {
        if (val && val.length > 0) {
            formFields.value.pay_period_type.options = val;
        }
    }
);

watch(
    () => store.payPeriodFrequencies,
    (val) => {
        if (val && val.length > 0) {
            formFields.value.pay_period_frequency.options = val;
        }
    }
);
</script>

<style scoped></style>
