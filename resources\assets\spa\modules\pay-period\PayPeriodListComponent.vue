<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="true"
        :has-export="false"
        :has-filters="true"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :actions="['delete']"
    >
        <template #filters>
            <FilterBlockWrapper label="Financial Year">
                <FinancialYearSelect v-model="store.filters.financialYear" />
            </FilterBlockWrapper>
        </template>
        <template #body-cell-period_name="{ props }">
            {{ props.dataItem.period_name }}
        </template>
        <template #body-cell-fiscal_year="{ props }">
            {{ props.dataItem.fiscal_year }}
        </template>
        <template #body-cell-start_date="{ props }">
            {{ props.dataItem.start_date }}
        </template>
        <template #body-cell-finish_date="{ props }">
            {{ props.dataItem.finish_date }}
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start space-x-2">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <button
                        @click="store.confirmDelete(props.dataItem)"
                        class="cursor-pointer"
                        title="Delete"
                    >
                        <icon :name="'delete'" :width="16" :height="16" />
                    </button>
                </Tooltip>
            </div>
        </template>
    </AsyncGrid>
    <PayPeriodForm />
</template>

<script setup>
import { onMounted } from 'vue';
import { usePayPeriodStore } from '@spa/stores/modules/pay-period/payPeriodStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import FinancialYearSelect from './FinancialYearSelect.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import PayPeriodForm from '@spa/modules/pay-period/PayPeriodForm.vue';

const store = usePayPeriodStore();

const columns = [
    {
        name: 'period_name',
        title: 'Period Name',
        field: 'period_name',
        sortable: true,
    },
    {
        name: 'fiscal_year',
        title: 'Fiscal Year',
        field: 'fiscal_year',
        sortable: true,
    },
    {
        name: 'start_date',
        title: 'Start Date',
        field: 'start_date',
        sortable: true,
    },
    {
        name: 'finish_date',
        title: 'Finish Date',
        field: 'finish_date',
        sortable: true,
    },
];

const initFilters = () => {
    store.filters = {
        financialYear: null,
    };
};

onMounted(() => {
    // initFilters();
});
</script>
