<template>
    <div class="grid grid-cols-2 gap-4 p-6">
        <field
            :id="'pay_period_type'"
            :name="'pay_period_type'"
            :label="'Pay Period Type'"
            :component="'dropdownTemplate'"
            :validator="requiredtrue"
            :data-items="store.payPeriodTypes ?? []"
            :text-field="'text'"
            :data-item-key="'value'"
            :value-field="'value'"
            :value-primitive="true"
            :default-item="{
                text: 'Select Pay Period Type',
                value: null,
            }"
            v-model="store.formData['pay_period_type']"
        >
            <template #dropdownTemplate="{ props }">
                <FormDropDown
                    v-bind="props"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                />
            </template>
        </field>

        <field
            :id="'pay_period_frequency'"
            :name="'pay_period_frequency'"
            :label="'Pay Period Frequency'"
            :component="'dropdownTemplate'"
            :validator="requiredtrue"
            :data-items="store.payPeriodFrequencies ?? []"
            :text-field="'text'"
            :data-item-key="'value'"
            :value-field="'value'"
            :value-primitive="true"
            :default-item="{
                text: 'Select Pay Period Frequency',
                value: null,
            }"
            v-model="store.formData['pay_period_frequency']"
        >
            <template #dropdownTemplate="{ props }">
                <FormDropDown
                    v-bind="props"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                />
            </template>
        </field>

        <field
            :id="'start_date_from'"
            :name="'start_date_from'"
            :label="'Start Date From'"
            :component="'dateTemplate'"
            :validator="requiredtrue"
            v-model="store.formData['start_date_from']"
        >
            <template #dateTemplate="{ props }">
                <FormDatePicker
                    v-bind="props"
                    :format="'dd-MM-yyyy'"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                />
            </template>
        </field>

        <field
            :id="'start_date_to'"
            :name="'start_date_to'"
            :label="'Start Date To'"
            :component="'dateTemplate'"
            :validator="requiredtrue"
            v-model="store.formData['start_date_to']"
        >
            <template #dateTemplate="{ props }">
                <FormDatePicker
                    v-bind="props"
                    :format="'dd-MM-yyyy'"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                />
            </template>
        </field>
    </div>
</template>

<script setup>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import { usePayPeriodStore } from '@spa/stores/modules/pay-period/payPeriodStore.js';

const store = usePayPeriodStore();
</script>

<style scoped></style>
