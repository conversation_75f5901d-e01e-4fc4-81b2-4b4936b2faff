<template lang="">
    <AsyncForm
        :form-config="formFields"
        :initial-values="store.formData"
        @submit="onSubmit"
        @change="onChange"
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :dialogTitle="'Add Register Improvement'"
        :override="true"
        :store="store"
    >
        <RegisterImprovementFormContent />
    </AsyncForm>
</template>
<script setup>
import { ref, watch, computed, inject, reactive } from 'vue';
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import { useStaffPositionStore } from '@spa/stores/modules/staff/useStaffPositionStore.js';
import RegisterImprovementFormContent from '@spa/modules/register-improvements/partials/RegisterImprovementFormContent.vue';

const store = useRegisterImprovementStore();
const staffStore = useStaffStore();
const staffPositionStore = useStaffPositionStore();

const initialFormValues = ref({
    logged_by: 'Bikash',
});

const staffNameOptions = computed(() => {
    return staffStore.all.map((item) => ({
        text: `${item.name_title} ${item.first_name} ${item.last_name}`,
        value: item.id,
    }));
});

const staffRoles = computed(() => {
    return staffPositionStore.all.map((item) => ({
        text: item.position,
        value: item.id,
    }));
});

const formFields = reactive({
    user_type: {
        type: 'select',
        label: 'User Type',
        required: true,
        placeholder: 'Select User Type',
        colSpan: 2,
        options: store.userTypes,
        textField: 'text',
        valueField: 'value',
    },
    staff_role: {
        type: 'select',
        label: 'Staff Role',
        required: true,
        placeholder: 'Select Staff Role',
        colSpan: 2,
        options: staffRoles,
        textField: 'text',
        valueField: 'value',
    },
    requested_by: {
        type: 'select',
        label: 'Requested By',
        required: true,
        placeholder: 'Select Requested By',
        colSpan: 2,
        options: staffNameOptions,
        textField: 'text',
        valueField: 'value',
        // replace: true,
        // component: FormTextArea,
    },
    logged_by: {
        type: 'text',
        label: 'Logged By',
        disabled: true,
        colSpan: 2,
    },
    category: {
        type: 'select',
        label: 'Category',
        required: true,
        placeholder: 'Select Category',
        colSpan: 2,
        options: store.category,
        textField: 'text',
        valueField: 'value',
    },
    category_add: {
        type: 'text',
        label: 'New Category',
        colSpan: 2,
        visible: false,
    },
    case_detail: {
        type: 'textarea',
        label: 'Case Detail',
        rows: 5,
        colSpan: 2,
    },
    escalated_to: {
        type: 'select',
        label: 'Escalated To',
        required: true,
        placeholder: 'Select Escalated To',
        colSpan: 2,
        options: staffNameOptions,
        textField: 'text',
        valueField: 'value',
    },
    action_taken: {
        type: 'textarea',
        label: 'Action Taken',
        rows: 5,
        colSpan: 2,
    },
    action_taken_prevent: {
        type: 'textarea',
        label: 'Action Taken to Prevent Reoccurence',
        rows: 5,
        colSpan: 2,
    },
    logged_date: {
        type: 'date',
        label: 'Logged Date',
        format: 'yyyy-MM-dd',
        colSpan: 2,
    },
});
const handleSubmit = (data) => {
    store.update(data);
};

watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            staffStore.fetchPaged();
            store.getFormConstants();
        }
    }
);

watch(
    () => store.userTypes,
    (val) => {
        if (val.length > 0) {
            formFields.user_type.options = val;
        }
    }
);

watch(
    () => store.formData,
    (val) => {
        console.log('value', val);
    }
);
</script>
<style lang=""></style>
