<script setup>
import { computed, onMounted, ref } from 'vue';
import KDropdown from '@spa/components/DropdownMenu/KDropdown.vue';
const props = defineProps({
    modelValue: {},
    style: {
        type: String,
        default: { width: 'fit-content', minWidth: '200px' },
    },
    options: {
        type: Array,
        default: () => [],
    },
    placeholder: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
});
const defaultItem = computed(() => ({
    label: props.placeholder,
    value: null,
}));
</script>

<template>
    <KDropdown
        text-field="label"
        value-field="value"
        :dataItems="options"
        :style="style"
        :popup-settings="{
            animate: false,
        }"
        tag-render="tagRender"
        :autoClose="true"
        :default-item="defaultItem"
        v-model="vModel"
        :valuePrimitive="true"
    />
</template>

<style scoped></style>
