<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="'Add Service'"
        :store="store"
    >
        <div class="p-4">
            <div class="p-2">
                <FormInput
                    name="facility_name"
                    label="Facility Name"
                    v-model="formData.facility_name"
                    :validation-message="store.errors?.facility_name"
                    :valid="!store.errors?.facility_name"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
            <div class="p-2">
                <FormInput
                    name="student_price"
                    label="Student Price"
                    v-model="formData.student_price"
                    :validation-message="store.errors?.student_price"
                    :valid="!store.errors?.student_price"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncForm.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useSetupServiceStore } from '@spa/stores/modules/service-providers/setup-services/useSetupServiceStore.js';
import { storeToRefs } from 'pinia';
// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const store = useSetupServiceStore();
const { formData } = storeToRefs(store);
</script>
