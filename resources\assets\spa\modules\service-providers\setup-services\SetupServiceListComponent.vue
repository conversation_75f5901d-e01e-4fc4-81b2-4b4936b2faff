<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
        <template #body-cell-is_active="{ props }">
            <badge :variant="props.dataItem?.is_active === 1 ? 'success' : 'error'">
                {{ props.dataItem?.is_active === 1 ? 'Active' : 'Inactive' }}
            </badge>
        </template>
        <template #body-cell-is_allow_student="{ props }">
            <badge :variant="props.dataItem?.is_allow_student === 1 ? 'success' : 'error'">
                {{ props.dataItem?.is_allow_student === 1 ? 'Yes' : 'No' }}
            </badge>
        </template>
        <template #body-cell-is_internal_only="{ props }">
            <badge :variant="props.dataItem?.is_internal_only === 1 ? 'success' : 'error'">
                {{ props.dataItem?.is_internal_only === 1 ? 'Yes' : 'No' }}
            </badge>
        </template>

        <template #filters>
            <FilterBlockWrapper label="Is Active">
                <EnumSelect
                    enum-class="GalaxyAPI\Enums\StatusEnum"
                    v-model="store.filters.isActive"
                />
            </FilterBlockWrapper>
        </template>
    </AsyncGrid>
    <SetupServiceForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted } from 'vue';
import { useSetupServiceStore } from '@spa/stores/modules/service-providers/setup-services/useSetupServiceStore.js';
import SetupServiceForm from '@spa/modules/service-providers/setup-services/SetupServiceForm.vue';
import Badge from '@spa/components/badges/Badge.vue';

import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';

const store = useSetupServiceStore();

const columns = [
    {
        field: 'service_name',
        title: 'Service Name',
        formatCellData: (val) => {
            return val?.services_name ?? 'N/A';
        },
    },
    {
        field: 'service_name',
        title: 'Date Type',
        name: 'date_type',
        formatCellData: (val) => {
            return val?.date_type ?? 'N/A';
        },
    },
    {
        field: 'service_name',
        name: 'is_allow_student',
        title: 'Allow Student to choose provider',
        replace: true,
    },
    {
        field: 'service_name',
        name: 'is_internal_only',
        title: 'Internal Only',
        replace: true,
    },
    {
        field: 'category',
        title: 'Category',
        formatCellData: (val) => {
            return val?.category ?? 'N/A';
        },
    },
    {
        field: 'facility_name',
        title: 'Facility Name',
    },
    {
        field: 'is_active',
        name: 'is_active',
        title: 'Active',
        replace: true,
    },
    {
        field: 'facility_price_type',
        title: 'Price Type',
    },
    {
        field: 'student_price',
        title: 'Student Price',
    },
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
