<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="true"
        :create-btn-label="'Add Teacher'"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :actions="['delete']"
    >
        <template #bulk-actions> </template>
        <template #body-cell-logged_date="{ props }">
            <FormatDate :date="props.dataItem?.updated_at" />
        </template>
        <template #body-cell-is_active="{ props }">
            <Badge
                :dark="true"
                :variant="parseInt(props.dataItem?.is_active) === 1 ? 'success' : 'error'"
                >{{ parseInt(props.dataItem?.is_active) === 1 ? 'Active' : 'Deactive' }}</Badge
            >
        </template>
        <template #body-cell-requester_name="{ props }">
            {{ props.dataItem?.requester?.full_name }}
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start space-x-2">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <Button
                        :variant="'icon'"
                        @click="router.visit(route('teacher-profile', [props.dataItem.id]))"
                        class="cursor-pointer"
                        title="View Teacher Profile"
                    >
                        <icon :name="'user'" :width="20" :height="20" />
                    </Button>
                </Tooltip>
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <Button
                        :variant="'icon'"
                        @click="store.confirmDelete(props.dataItem)"
                        class="cursor-pointer"
                        title="Delete"
                    >
                        <icon :name="'delete'" :width="20" :height="20" />
                    </Button>
                </Tooltip>
            </div>
        </template>
    </AsyncGrid>
    <TeacherForm />
</template>

<script setup>
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import Badge from '@spa/components/badges/Badge.vue';
import Select from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import TeacherForm from '@spa/modules/teacher/TeacherForm.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { router } from '@inertiajs/vue3';

const store = useTeacherStore();
const columns = [
    {
        name: 'staff_number',
        title: 'Staff Number',
        field: 'staff_number',
        sortable: true,
    },
    {
        name: 'full_name',
        title: 'Teacher/Trainee',
        field: 'full_name',
        sortable: false,
    },
    {
        name: 'username',
        title: 'Username',
        field: 'username',
        sortable: true,
    },
    {
        name: 'email',
        title: 'Email Address',
        field: 'email',
        sortable: true,
    },
    {
        name: 'is_active',
        title: 'Status',
        field: 'is_active',
        sortable: true,
        replace: true,
    },
    {
        name: 'address',
        title: 'Address',
        field: 'address',
        sortable: true,
    },
    {
        name: 'city_town',
        title: 'City Town',
        field: 'city_town',
        sortable: true,
    },
];
const initFilters = () => {
    store.filters = {};
};

const initInertiaData = () => {};
onMounted(() => {
    initFilters();
});
</script>
