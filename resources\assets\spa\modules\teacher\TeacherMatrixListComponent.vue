<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="true"
        :create-btn-label="'Add Teacher Matrix'"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
    >
        <template #bulk-actions> </template>
        <template #body-cell-knowledge_level="{ props }">
            <Badge :variant="getBadgeVariant(props.dataItem?.knowledge_level_id)">{{
                props.dataItem?.knowledge_level
            }}</Badge>
        </template>
        <template #body-cell-course="{ props }">
            <div class="inline">
                <span class="text-gray-500">
                    {{ props.dataItem?.course_code }}
                </span>
                -
                <p class="inline w-auto text-sm leading-5 text-gray-700">
                    {{ props.dataItem?.course_name }}
                </p>
            </div>
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start gap-1">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <Button
                        :variant="'icon'"
                        @click="store.edit(props.dataItem)"
                        class="cursor-pointer"
                        title="Edit"
                    >
                        <icon :name="'pencil'" :width="20" :height="20" />
                    </Button>
                </Tooltip>
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <Button
                        :variant="'icon'"
                        @click="store.confirmDelete(props.dataItem)"
                        class="cursor-pointer"
                        title="Delete"
                    >
                        <icon :name="'delete'" :width="20" :height="20" />
                    </Button>
                </Tooltip>
            </div>
        </template>
    </AsyncGrid>
    <TeacherMatrixForm />
</template>

<script setup>
import { useTeacherMatrixStore } from '@spa/stores/modules/teacher/useTeacherMatrixStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import Badge from '@spa/components/badges/Badge.vue';
import Select from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import TeacherMatrixForm from '@spa/modules/teacher/TeacherMatrixForm.vue';
import Button from '@spa/components/Buttons/Button.vue';

const store = useTeacherMatrixStore();
const columns = [
    {
        name: 'teacher_name',
        title: 'Name',
        field: 'teacher_name',
        sortable: true,
    },
    {
        name: 'subject_code',
        title: 'Subject Id',
        field: 'subject_code',
        sortable: false,
    },
    {
        name: 'subject_name',
        title: 'Subject Name',
        field: 'subject_name',
        sortable: true,
    },
    {
        name: 'course',
        title: 'Course',
        field: 'course',
        sortable: true,
        replace: true,
    },
    {
        name: 'knowledge_level',
        title: 'Knowledge Level',
        field: 'knowledge_level',
        sortable: true,
        replace: true,
    },
];

const getBadgeVariant = (value) => {
    let badgeMapping = {
        1: 'purple',
        2: 'secondary',
    };
    return badgeMapping[value] || 'default';
};
const initFilters = () => {
    store.filters = {};
};

const initInertiaData = () => {};
onMounted(() => {
    initFilters();
});
</script>
