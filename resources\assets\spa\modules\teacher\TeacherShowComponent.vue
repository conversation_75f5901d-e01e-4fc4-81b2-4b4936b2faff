<template>
    <ProfileHeaderWithTabs :title="'Teacher Profile'" :tabs="tabs" :actions="actions">
        <template #buttongroup>
            <Button :variant="'primary'" size="xs">
                <span :class="'text-white'">
                    <icon :name="'mail'" :fill="'#ffffff'" size="16" />
                </span>
                <span>Send Mail</span>
            </Button>
            <Button :variant="'primary'" size="xs">
                <span :class="'text-white'">
                    <icon :name="'sms'" />
                </span>
                <span>Send SMS</span>
            </Button>
        </template>
    </ProfileHeaderWithTabs>
</template>
<script setup>
import ProfileHeaderWithTabs from '@spa/modules/common/ProfileHeaderWithTabs.vue';
import Button from '@spa/components/Buttons/Button.vue';

const tabs = [
    {
        name: 'Profile',
        slug: 'profile',
    },
    {
        name: 'Communication Logs',
        slug: 'communication-logs',
    },
    {
        name: 'Teacher Matrix',
        slug: 'teacher-matrix',
    },
    {
        name: 'Timetable',
        slug: 'timetable',
    },
    {
        name: 'Documents',
        slug: 'documents',
    },
    {
        name: 'Leave Info',
        slug: 'leave-info',
    },
];
</script>
<style lang=""></style>
