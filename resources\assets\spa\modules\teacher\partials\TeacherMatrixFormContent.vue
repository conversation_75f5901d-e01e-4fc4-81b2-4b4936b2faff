<template>
    <!-- {{ store.formData }} -->
    <div class="flex-1 space-y-6 overflow-y-auto px-6 py-4">
        <Card :pt="{ root: 'relative p-4 rounded-lg' }">
            <template #content>
                <fieldset>
                    <div class="grid flex-1 grid-cols-1 gap-6 overflow-y-auto">
                        <field
                            :id="'teacher_id'"
                            :name="'teacher_id'"
                            :label="'Select Teacher Trainer'"
                            :component="'teacherSelectTemplate'"
                            :value-premitive="true"
                            v-model="store.formData.teacher_id"
                            :required="true"
                        >
                            <template #teacherSelectTemplate="{ props }">
                                <TeacherSelect v-bind="props" />
                            </template>
                        </field>
                        <field
                            :id="'course_status'"
                            :name="'course_status'"
                            :label="'Course Status'"
                            :data-items="[
                                {
                                    label: 'Active',
                                    value: 1,
                                },
                                {
                                    label: 'Inactive',
                                    value: 0,
                                },
                            ]"
                            :default-value="1"
                            :component="FormRadioGroup"
                            v-model="store.formData.course_status"
                            :required="true"
                            :validator="requiredtrue"
                            :layout="'horizontal'"
                            @change="
                                () => {
                                    courseStore.filters.course_status =
                                        store.formData.course_status;
                                }
                            "
                            :indicaterequired="true"
                        />
                        <field
                            :id="'course_id'"
                            :name="'course_id'"
                            :label="'Select Course'"
                            :component="'coursesTemplate'"
                            :value-premitive="true"
                            v-model="store.formData.course_id"
                            :indicaterequired="true"
                        >
                            <template #coursesTemplate="{ props }">
                                <CoursesSelect v-bind="props" /> </template
                        ></field>

                        <field
                            :id="'subject_id'"
                            :name="'subject_id'"
                            :label="'Select Subject'"
                            :component="'subjectsTemplate'"
                            :data-items="countryStore.all"
                            :text-field="'name'"
                            :data-item-key="'id'"
                            :value-field="'id'"
                            :value-primitive="true"
                            :default-item="{
                                name: 'Select Subject',
                                id: null,
                            }"
                            v-model="store.formData.subject_id"
                            :required="true"
                            :validator="requiredtrue"
                            :indicaterequired="true"
                        >
                            <template #subjectsTemplate="{ props }">
                                <SubjectsCheckbox v-bind="props" /> </template
                        ></field>

                        <field
                            :id="'knowledge_level'"
                            :name="'knowledge_level'"
                            :label="'Knowledge Level'"
                            :component="FormDropDown"
                            :data-items="knowledgeLevelOptions"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            :default-item="{
                                text: 'Select Knowledge Level',
                                value: null,
                            }"
                            v-model="store.formData.knowledge_level"
                            :required="true"
                            :validator="requiredtrue"
                        />
                    </div>
                </fieldset>
            </template>
        </Card>
    </div>
</template>
<script setup>
import { computed } from 'vue';
import { Field } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import Card from '@spa/components/Card/Card.vue';
import { teacherConfig } from '@spa/config/teacherConfig.js';
import { useCountryStore } from '@spa/stores/modules/config/useCountryStore.js';
import TeacherSelect from '@spa/modules/teacher/partials/TeacherSelect.vue';
import SubjectsCheckbox from '@spa/modules/teacher/partials/SubjectsCheckbox.vue';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';
import { useCoursesStore } from '@spa/stores/modules/courses/useCoursesStore.js';

const props = defineProps({
    store: {
        type: Object,
        required: true,
    },
});

const courseStore = useCoursesStore();

const countryStore = useCountryStore();

const { knowledge_level_code } = teacherConfig;

const knowledgeLevelOptions = computed(() => {
    return Object.entries(knowledge_level_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});
</script>
<style lang=""></style>
