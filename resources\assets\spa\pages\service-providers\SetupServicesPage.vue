<script setup>
import { Head } from '@inertiajs/vue3';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import SetupServiceListComponent from '@spa/modules/service-providers/setup-services/SetupServiceListComponent.vue';
</script>

<template>
    <Layout :no-spacing="true">
        <Head title="Setup Services - Services setup facility" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Setup Services - Services setup facility" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <SetupServiceListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
