<script setup>
import TeacherMatrixListComponent from '@spa/modules/teacher/TeacherMatrixListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Teacher Matrix List" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Teacher Matrix List" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <TeacherMatrixListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
