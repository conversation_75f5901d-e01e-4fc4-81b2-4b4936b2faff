<script setup>
import TeacherListComponent from '@spa/modules/teacher/TeacherListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Teachers List" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Teachers List" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <TeacherListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
