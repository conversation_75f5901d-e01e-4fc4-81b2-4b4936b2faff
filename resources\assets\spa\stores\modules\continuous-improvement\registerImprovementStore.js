import useConfirm from '@spa/services/useConfirm';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { defineStore } from 'pinia';
import { reactive, ref, onMounted, watch } from 'vue';
import apiClient from '@spa/services/api.client.js';

export const useRegisterImprovementStore = defineStore('useRegisterImprovementStore', () => {
    const storeUrl = ref('v2/tenant/register-improvement');
    const confirm = useConfirm();

    const commonStoreProps = useCommonStore(storeUrl.value);
    const { selected, remove, fetchPaged, errors, all } = commonStoreProps;

    const userTypes = ref([]);
    const categories = ref([]);

    const getFormConstants = async () => {
        try {
            const response = await apiClient.get(`/api/${storeUrl.value}/form-constants`);
            console.log('defaults', response);
            userTypes.value = convertToTextValue(response.user_types);
            categories.value = convertToTextValue(response.catagory);
            return response;
        } catch (error) {
            console.error('Error fetching form constants:', error);
            throw error;
        }
    };

    const convertToTextValue = (obj) => {
        return Object.entries(obj).map(([key, value]) => ({
            text: value,
            value: key,
        }));
    };

    const dropdowns = reactive({
        categories: [],
        loggedBy: [],
        requestedBy: [],
    });

    watch(
        () => all.value,
        (newVal) => {
            if (newVal.length > 0) {
                dropdowns.categories = getUniqueOptions('category');
                dropdowns.loggedBy = getUniqueOptions(
                    'logged_by_name',
                    'logged_by_name',
                    'logged_by'
                );
                dropdowns.requestedBy = getUniqueOptions(
                    'requester.full_name',
                    'requester.full_name',
                    'requested_by'
                );
            }
        },
        { immediate: true }
    );

    const getUniqueOptions = (key, labelKey = null, valueKey = null, labelMap = null) => {
        const map = new Map();

        all.value.forEach((item) => {
            let value = valueKey ? getNested(item, valueKey) : item[key];
            let rawLabel = labelKey ? getNested(item, labelKey) : item[key];
            let label = labelMap && value in labelMap ? labelMap[value] : rawLabel;

            if (label && !map.has(value)) {
                map.set(value, { label, value });
            }
        });

        return [{ label: 'All', value: 'all' }, ...Array.from(map.values())];
    };

    const getNested = (obj, path) => {
        return path.split('.').reduce((acc, part) => acc?.[part], obj);
    };

    return {
        ...commonStoreProps,
        getFormConstants,
        userTypes,
        categories,
        dropdowns,
    };
});
