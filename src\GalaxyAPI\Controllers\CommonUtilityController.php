<?php

namespace GalaxyAPI\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CommonUtilityController extends Controller
{
    public function getEnumOptions(
        Request $request
    ) {
        $request->validate([
            'enum' => [
                'required',
                'string',
            ],
        ]);
        $enumClass = $request->enum;
        if (! class_exists($enumClass)) {
            throw ValidationException::withMessages([
                'enum' => 'Enum class not found',
            ]);
        }
        if (! $enumClass::allow_fetch) {
            throw ValidationException::withMessages([
                'enum' => 'Enum class not allowed to fetch',
            ]);
        }

        return ajaxSuccess([
            'data' => $enumClass::getOptionsArray(),
        ], 'Enum options fetched successfully');

    }
}
