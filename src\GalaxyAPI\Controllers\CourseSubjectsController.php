<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\CourseSubjects;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Resources\CourseSubjectsResource;

class CourseSubjectsController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: CourseSubjects::class,
            storeRequest: EmptyRequest::class,
            updateRequest: EmptyRequest::class,
            resource: CourseSubjectsResource::class,
        );
    }
}
