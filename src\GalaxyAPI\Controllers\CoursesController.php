<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\Courses;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Resources\CoursesResource;
use Illuminate\Support\Facades\Auth;

class CoursesController extends CrudBaseController
{
    public function __construct()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()->college_id,
        ];

        parent::__construct(
            model: Courses::class,
            storeRequest: EmptyRequest::class,
            updateRequest: EmptyRequest::class,
            resource: CoursesResource::class,
        );
    }
}
