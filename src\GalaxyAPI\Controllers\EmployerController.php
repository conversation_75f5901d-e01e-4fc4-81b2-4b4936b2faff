<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\Country;
use App\Model\v2\Employer;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Requests\EmployerRequest;
use GalaxyAPI\Resources\EmployerResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class EmployerController extends CrudBaseController
{
    public function __construct()
    {
        $this->withAll = [
            'industry',
        ];
        $this->loadAll = [
            'industry',
        ];
        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];
        // Temporarily comment out delete scope to test
        // $this->deleteScopeWithValue = [
        //     'collegeId' => Auth::user()?->college_id,
        // ];

        parent::__construct(
            model: Employer::class,
            storeRequest: EmployerRequest::class,
            updateRequest: EmptyRequest::class,
            resource: EmployerResource::class,
        );
    }

    public function store()
    {
        $request = resolve($this->storeRequest);
        $user = auth()->user();
        try {
            $data = $request->validated();
            $data['college_id'] = $user->college_id;
            $data['created_by'] = $user->id;
            $data['updated_by'] = $user->id;
            $employer = $this->model::create($data);

            return $this->success('Employer created successfully.', [
                'data' => new $this->resource($employer),
            ]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    public function update($id)
    {
        $request = resolve($this->updateRequest);
        $user = Auth::user();
        try {
            $employer = $this->model::where('college_id', $user->college_id)->findOrFail($id);
            $data = $request->validated();
            $data['updated_by'] = $user->id;
            $employer->update($data);

            return $this->success('Employer updated successfully.', [
                'data' => new $this->resource($employer),
            ]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    public function formConstants()
    {
        $collegeId = Auth::user()?->college_id;
        $arrStatus = Config::get('constants.arrStatus');
        $arrIndustries = Config::get('constants.arrTraningOrgazinationIndustryCode');
        $arrCountries = Country::whereIn('college_id', [$collegeId, 0])->orderBy('name')->pluck('name', 'id')->toArray();

        return ajaxSuccess([
            'status_options' => $arrStatus,
            'industry_options' => $arrIndustries,
            'country_options' => $arrCountries,
        ], '');
    }
}
