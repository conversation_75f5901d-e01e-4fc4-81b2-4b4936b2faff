<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\SetupServices;
use GalaxyAPI\Requests\SetupServiceRequest;
use GalaxyAPI\Resources\SetupServiceResource;

class SetupServiceController extends CrudBaseController
{
    public function __construct()
    {
        $this->withAll = [
            'serviceName',
            'category',
        ];
        $this->loadAll = [
            'serviceName',
            'category',
        ];
        parent::__construct(
            model: SetupServices::class,
            storeRequest: SetupServiceRequest::class,
            updateRequest: SetupServiceRequest::class,
            resource: SetupServiceResource::class,
        );
    }
}
