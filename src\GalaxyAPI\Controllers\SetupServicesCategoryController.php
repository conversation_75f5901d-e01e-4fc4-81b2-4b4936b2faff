<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\SetupServicesCategory;
use GalaxyAPI\Requests\SetupServicesCategoryRequest;
use GalaxyAPI\Resources\SetupServicesCategoryResource;

class SetupServicesCategoryController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: SetupServicesCategory::class,
            storeRequest: SetupServicesCategoryRequest::class,
            updateRequest: SetupServicesCategoryRequest::class,
            resource: SetupServicesCategoryResource::class,
        );
    }
}
