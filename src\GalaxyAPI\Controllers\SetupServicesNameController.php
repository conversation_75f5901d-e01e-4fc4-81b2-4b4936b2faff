<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\SetupServicesName;
use GalaxyAPI\Requests\SetupServicesNameRequest;
use GalaxyAPI\Resources\SetupServicesNameResource;

class SetupServicesNameController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: SetupServicesName::class,
            storeRequest: SetupServicesNameRequest::class,
            updateRequest: SetupServicesNameRequest::class,
            resource: SetupServicesNameResource::class,
        );
    }
}
