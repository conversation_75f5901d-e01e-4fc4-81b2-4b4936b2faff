<?php

namespace GalaxyAPI\Controllers;

use App\Exports\USIVerifyStudentExport;
use App\Model\v2\Student;
use Carbon\Carbon;
use GalaxyAPI\Requests\StudentRequest;
use GalaxyAPI\Resources\StudentResource;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class StudentController extends CrudBaseController
{
    public function __construct()
    {

        $this->scopeWithValue = [
            'collegeId' => Auth::user()->college_id,
        ];

        parent::__construct(
            model: Student::class,
            storeRequest: StudentRequest::class,
            updateRequest: StudentRequest::class,
            resource: StudentResource::class,
        );
    }

    public function exportUSIStudents()
    {
        $current_date = Carbon::now()->format('Y-m-d H:i:s.u');

        return Excel::download(new USIVerifyStudentExport, "USI_Validation_Export-$current_date.xlsx");
    }
}
