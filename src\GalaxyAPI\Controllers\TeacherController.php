<?php

namespace GalaxyAPI\Controllers;

use App\Model\Teacher;
use GalaxyAPI\Requests\TeacherRequest;
use GalaxyAPI\Resources\TeacherResource;
use Illuminate\Support\Facades\Auth;

class TeacherController extends CrudBaseController
{
    public function __construct()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()->college_id,
        ];

        $this->loadAll = [
            'users',
        ];

        $this->withAll = [
            'users',
        ];

        parent::__construct(
            model: Teacher::class,
            storeRequest: TeacherRequest::class,
            updateRequest: TeacherRequest::class,
            resource: TeacherResource::class,
        );
    }

    public function updateUserId(TeacherRequest $request, $id)
    {
        $teacher = Teacher::find($id);
        $teacher->user_id = $request->input('user_id');
        $teacher->save();

        return ajaxSuccess([], '');
    }
}
