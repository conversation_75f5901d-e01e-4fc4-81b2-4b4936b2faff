<?php

namespace GalaxyAPI\Controllers;

use App\Model\TeacherMatrix;
use GalaxyAPI\Requests\TeacherMatrixRequest;
use GalaxyAPI\Resources\TeacherMatrixResource;
use Illuminate\Support\Facades\Auth;

class TeacherMatrixController extends CrudBaseController
{
    public function __construct()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()->college_id,
        ];

        $this->loadAll = [
            'subject',
            'teacher',
            'course',
        ];

        $this->withAll = [
            'subject',
            'teacher',
            'course',
        ];

        parent::__construct(
            model: TeacherMatrix::class,
            storeRequest: TeacherMatrixRequest::class,
            updateRequest: TeacherMatrixRequest::class,
            resource: TeacherMatrixResource::class,
        );
    }
}
