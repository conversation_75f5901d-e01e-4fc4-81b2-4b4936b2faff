<?php

namespace GalaxyAPI\Controllers;

use App\Model\Teacher;
use GalaxyAPI\Requests\TeacherRequest;
use GalaxyAPI\Resources\TeacherResource;
use Illuminate\Support\Facades\Auth;

class UsersController extends CrudBaseController
{
    public function __construct()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()->college_id,
        ];

        $this->loadAll = [
            'users',
        ];

        $this->withAll = [
            'users',
        ];

        parent::__construct(
            model: Teacher::class,
            storeRequest: TeacherRequest::class,
            updateRequest: TeacherRequest::class,
            resource: TeacherResource::class,
        );
    }
}
