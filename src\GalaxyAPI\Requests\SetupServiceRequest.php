<?php

namespace GalaxyAPI\Requests;

use GalaxyAPI\Enums\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class SetupServiceRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'services_name_id' => 'required',
            'category_id' => 'required',
            'facility_name' => 'required',
            'facility_price_type' => [
                'required',
                StatusEnum::getValidationString(),
            ],
            'student_price' => [
                'required',
                'decimal:10,2',
            ],
            'is_active' => [
                'nullable',
                StatusEnum::getValidationString(),
            ],
        ];
    }
}
