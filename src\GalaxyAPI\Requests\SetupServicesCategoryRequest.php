<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SetupServicesCategoryRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'college_id' => [
                'required',
                'exists:rto_colleges,id',
            ],
            'service_name_id' => [
                'required',
                'exists:rto_setup_services_name,id',
            ],
            'category_name' => 'required',
        ];
    }
}
