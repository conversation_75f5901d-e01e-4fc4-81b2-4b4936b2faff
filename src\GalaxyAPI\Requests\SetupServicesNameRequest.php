<?php

namespace GalaxyAPI\Requests;

use GalaxyAPI\Enums\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class SetupServicesNameRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'college_id' => [
                'required',
                'exists:rto_colleges,id',
            ],
            'services_name' => 'required|string|min:3|max:255',
            // todo: find out all possible date types and create enum
            'date_type' => 'required',
            'is_allow_student' => [
                'required',
                StatusEnum::getValidationString(),
            ],
            'is_internal_only' => [
                'required',
                StatusEnum::getValidationString(),
            ],
        ];
    }
}
