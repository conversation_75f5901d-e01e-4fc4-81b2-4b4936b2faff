<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class TeacherMatrixRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'teacher_id' => 'required|integer|exists:rto_staff_and_teacher,id',
            'course_id' => 'required|integer|exists:rto_courses,id',
            'subject_id' => 'required|array',
            'subject_id.*' => 'required|exists:rto_subject,id',
            'knowledge_level' => 'required|in:0,1',
            'course_status' => 'nullable|in:0,1',
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'updated_by' => Auth::user()->id,
            'created_by' => Auth::user()->id,
            'college_id' => Auth::user()->college_id,
            'course_status' => $this->input('course_status', 1),
        ]);
    }

    public function messages()
    {
        return [
            'teacher_id.required' => 'Teacher is required.',
            'course_id.required' => 'Course is required.',
            'subject_id.required' => 'At least one subject must be selected.',
            'subject_id.*.exists' => 'One or more selected subjects are invalid.',
        ];
    }
}
