<?php

namespace GalaxyAPI\Requests;

use GalaxyAPI\Traits\HasRequestHelpers;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

// Note: it is just used to avoid the error of empty request  just use this as placeholder
class TeacherRequest extends FormRequest
{
    use HasRequestHelpers;

    public function rules(): array
    {
        return [
            'name_title' => 'nullable|string|max:20',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'country' => 'required|string|max:100',
            'city_town' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postcode' => 'required|string|max:10',
            'phone' => 'required|string|max:20',
            'mobile' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'personal_email' => 'required|email|max:255',
            'signatory_text' => 'required|string|max:255',
            'staff_number' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'is_active' => 'nullable',
            'birth_date' => 'required|date',
            'joining_date' => 'nullable|date|after_or_equal:birth_date',
            'gender' => 'nullable|in:M,F,X',
            'atsi_code' => 'nullable|integer',
            'highest_qualification_code' => 'nullable|integer',
            'highest_qualification_place_code' => 'nullable|integer',
            'work_contract_code' => 'nullable|integer',
            'staff_work_level_code' => 'nullable|integer',
            'organisational_unit_code' => 'nullable|integer',
            'work_sector_code' => 'nullable|integer',
            'function_code' => 'nullable|integer',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
            'staff_type' => 'required|string|max:255',
            'updated_by' => 'required|integer',
            'created_by' => 'required|integer',
            'college_id' => 'required|integer',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'is_active' => $this->input('is_active', 1),
            'staff_type' => $this->input('staff_type', 'Staff-Teacher'),
            'joining_date' => $this->input('joining_date', now()->toDateString()), // Default to today
            'personal_email' => $this->input('personal_email', null),
            'updated_by' => Auth::user()->id,
            'created_by' => Auth::user()->id,
            'college_id' => Auth::user()->college_id,
        ]);
    }
}
