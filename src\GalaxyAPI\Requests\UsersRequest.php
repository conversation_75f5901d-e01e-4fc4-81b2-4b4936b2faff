<?php

namespace GalaxyAPI\Requests;

use GalaxyAPI\Traits\HasRequestHelpers;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

// Note: it is just used to avoid the error of empty request  just use this as placeholder
class UsersRequest extends FormRequest
{
    use HasRequestHelpers;

    public function rules(): array
    {
        return [
            'name' => 'nullable|string|max:20',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
            'role_id' => 'required|integer',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'college_id' => 'required|integer',
            'security_question' => 'nullable|string|max:255',
            'security_answer' => 'nullable|string|max:255',
        ];
    }

    // protected function prepareForValidation()
    // {
    //     $this->merge([
    //         'is_active' => $this->input('is_active', 1),
    //         'staff_type' => $this->input('staff_type', 'Staff-Teacher'),
    //         'joining_date' => $this->input('joining_date', now()->toDateString()), // Default to today
    //         'personal_email' => $this->input('personal_email', null),
    //         'updated_by' => Auth::user()->id,
    //         'created_by' => Auth::user()->id,
    //         'college_id' => Auth::user()->college_id,
    //     ]);
    // }
}
