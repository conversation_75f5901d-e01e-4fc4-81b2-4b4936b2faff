<?php

namespace GalaxyAPI\Resources;

use GalaxyAPI\Enums\ServiceSetupFacilityPriceTypeEnum;
use GalaxyAPI\Enums\SetupServiceNameDateTypeEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SetupServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'college' => $this->whenLoaded('college', function () {
                return [
                    'id' => $this->college->id,
                    'college_name' => $this->college->college_name,
                    'RTO_code' => $this->college->RTO_code,
                ];
            }),
            'services_name_id' => $this->services_name_id,
            'service_name' => $this->whenLoaded('serviceName', function () {
                return [
                    'id' => $this->serviceName->id,
                    'services_name' => $this->serviceName->services_name,
                    'date_type' => SetupServiceNameDateTypeEnum::from($this->serviceName->date_type)?->getFormatedName(),
                    'is_allow_student' => $this->serviceName->is_allow_student,
                    'is_internal_only' => $this->serviceName->is_internal_only,
                ];
            }),
            'category_id' => $this->category_id,
            'category' => $this->whenLoaded('category', function () {
                return [
                    'id' => $this->category->id,
                    'category' => $this->category->category_name,
                ];
            }),
            'facility_name' => $this->facility_name,
            'facility_price_type' => ServiceSetupFacilityPriceTypeEnum::from($this->facility_price_type)?->getFormatedName(),
            'student_price' => $this->student_price,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
        ];
    }
}
