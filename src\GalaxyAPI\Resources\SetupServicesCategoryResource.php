<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SetupServicesCategoryResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'college' => $this->whenLoaded('college', function () {
                return [
                    'id' => $this->college->id,
                    'college_name' => $this->college->college_name,
                    'RTO_code' => $this->college->RTO_code,
                ];
            }),
            'service_name_id' => $this->service_name_id,
            'service_name' => $this->whenLoaded('serviceName', function () {
                return [
                    'id' => $this->serviceName->id,
                    'services_name' => $this->serviceName->services_name,
                ];
            }),
            'category_name' => $this->category_name,
        ];
    }
}
