<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SetupServicesNameResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'college' => $this->whenLoaded('college', function () {
                return [
                    'id' => $this->college->id,
                    'college_name' => $this->college->college_name,
                    'RTO_code' => $this->college->RTO_code,
                ];
            }),
            'services_name' => $this->services_name,
            'date_type' => $this->date_type,
            'is_allow_student' => $this->is_allow_student,
            'is_internal_only' => $this->is_internal_only,
        ];
    }
}
