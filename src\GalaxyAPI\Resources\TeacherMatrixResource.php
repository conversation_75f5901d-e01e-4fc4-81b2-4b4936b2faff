<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Config;

class TeacherMatrixResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $arrKnowledgeLevel = Config::get('constants.arrKnowledgeLevel');

        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'teacher_id' => $this->teacher_id,
            'teacher_name' => $this->teacher?->name_title.' '.$this->teacher?->first_name.' '.$this->teacher?->last_name,
            'course_status' => $this->status,
            'course_id' => $this->course_id,
            'course_code' => $this->course?->course_code,
            'course_name' => $this->course?->course_name,
            'national_code' => $this->course?->national_code,
            'cricos_code' => $this->course?->cricos_code,
            'subject_id' => $this->subject_id,
            'subject_code' => $this->subject?->subject_code,
            'subject_name' => $this->subject?->subject_name,
            'knowledge_level_id' => $this->knowledge_level,
            'knowledge_level' => $arrKnowledgeLevel[$this->knowledge_level],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
        ];
    }
}
