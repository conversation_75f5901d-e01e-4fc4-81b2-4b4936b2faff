<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TeacherResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'username' => $this->whenLoaded('users', function () {
                return $this->users->username;
            }),
            'college_id' => $this->college_id,
            'user_id' => $this->user_id,
            'full_name' => $this->name_title.' '.$this->first_name.' '.$this->last_name,
            'name_title' => $this->name_title,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'country_id' => $this->country,
            'address' => $this->address,
            'city_town' => $this->city_town,
            'state' => $this->state,
            'postcode' => $this->postcode,
            'phone' => $this->phone,
            'mobile' => $this->mobile,
            'email' => $this->email,
            'personal_email' => $this->personal_email,
            'birth_date' => $this->birth_date,
            'gender' => $this->gender,
            'atsi_code' => $this->atsi_code,
            'joining_date' => $this->joining_date,
            'highest_qualification_code' => $this->highest_qualification_code,
            'highest_qualification_place_code' => $this->highest_qualification_place_code,
            'signatory_text' => $this->signatory_text,
            'work_contract_code' => $this->work_contract_code,
            'staff_work_level_code' => $this->staff_work_level_code,
            'organisational_unit_code' => $this->organisational_unit_code,
            'work_sector_code' => $this->work_sector_code,
            'function_code' => $this->function_code,
            'staff_number' => $this->staff_number,
            'position' => $this->position,
            'staff_type' => $this->staff_type,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
        ];
    }
}
