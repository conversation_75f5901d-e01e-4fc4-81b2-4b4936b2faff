<?php

namespace GalaxyAPI\Traits;

use Illuminate\Support\Str;

trait EnumUtilitiesTrait
{
    public static function getLabel(
        $label
    ): string {
        return str_replace('_', ' ', Str::title($label));
    }

    public static function getOptionsArray(): array
    {
        return array_map(
            fn ($case) => [
                'value' => $case->value,
                'label' => self::getLabel($case->name),
            ],
            self::cases()
        );
    }

    public static function getValidationString(): string
    {
        $values = self::cases();
        $values = array_map(function ($value) {
            return $value->value;
        }, $values);
        $values = implode(',', $values);

        return "in:{$values}";
    }

    public function getFormatedName()
    {
        return self::getLabel($this->name);
    }
}
